import React from 'react';
import { useAuth } from '../contexts/AuthContext';

function AdminTest() {
  const { user, login, logout } = useAuth();

  const handleAdminLogin = async () => {
    const success = await login('<EMAIL>', 'admin123');
    console.log('Admin login result:', success);
  };

  const handleStudentLogin = async () => {
    const success = await login('<EMAIL>', 'password123');
    console.log('Student login result:', success);
  };

  return (
    <div className="fixed top-4 right-4 bg-white p-4 rounded-lg shadow-lg border z-50">
      <h3 className="font-bold mb-2">Auth Test Panel</h3>
      
      <div className="mb-3">
        <p className="text-sm">
          <strong>User:</strong> {user?.name || 'Not logged in'}
        </p>
        <p className="text-sm">
          <strong>Email:</strong> {user?.email || 'N/A'}
        </p>
        <p className="text-sm">
          <strong>Role:</strong> {user?.role || 'N/A'}
        </p>
        <p className="text-sm">
          <strong>Is Admin:</strong> {user?.role === 'admin' ? 'YES' : 'NO'}
        </p>
      </div>

      <div className="space-y-2">
        {!user ? (
          <>
            <button
              onClick={handleStudentLogin}
              className="w-full bg-blue-500 text-white px-3 py-1 rounded text-sm"
            >
              Login as Student
            </button>
            <button
              onClick={handleAdminLogin}
              className="w-full bg-red-500 text-white px-3 py-1 rounded text-sm"
            >
              Login as Admin
            </button>
          </>
        ) : (
          <button
            onClick={logout}
            className="w-full bg-gray-500 text-white px-3 py-1 rounded text-sm"
          >
            Logout
          </button>
        )}
      </div>
    </div>
  );
}

export default AdminTest;
