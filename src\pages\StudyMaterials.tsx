import React, { useState, useEffect } from 'react';
import {
  BookOpen,
  Download,
  Play,
  Search,
  Filter,
  Star,
  ShoppingCart,
  Eye,
  FileText,
  Video,
  HeadphonesIcon,
  Plus,
  Edit,
  Trash2,
  Upload,
  X,
  Save,
  AlertCircle
} from 'lucide-react';
import { studyMaterialsAPI } from '../services/api';
import { StudyMaterial } from '../types';
import { useAuth } from '../contexts/AuthContext';
import AdminTest from '../components/AdminTest';

function StudyMaterials() {
  const { user, login } = useAuth();
  const [subjectFilter, setSubjectFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [priceFilter, setPriceFilter] = useState('all');
  const [materials, setMaterials] = useState<StudyMaterial[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<StudyMaterial | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'PDF' as 'PDF' | 'Video' | 'Audio',
    subject: '',
    price: 0,
    originalPrice: 0,
    author: '',
    isPremium: false,
    thumbnailUrl: '',
    pages: 0,
    duration: '',
    fileSize: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Filter options
  const subjects = ['all', 'Current Affairs', 'Polity', 'Economics', 'Geography', 'History', 'Science & Technology'];
  const types = ['all', 'PDF', 'Video', 'Audio'];

  useEffect(() => {
    fetchMaterials();
  }, []);

  const fetchMaterials = async () => {
    try {
      setLoading(true);
      const data = await studyMaterialsAPI.getAll();
      setMaterials(data);
    } catch (error) {
      console.error('Error fetching materials:', error);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) errors.title = 'Title is required';
    if (!formData.description.trim()) errors.description = 'Description is required';
    if (!formData.subject.trim()) errors.subject = 'Subject is required';
    if (!formData.author.trim()) errors.author = 'Author is required';
    if (formData.price < 0) errors.price = 'Price must be positive';
    if (!formData.thumbnailUrl.trim()) errors.thumbnailUrl = 'Thumbnail URL is required';
    if (!formData.fileSize.trim()) errors.fileSize = 'File size is required';

    if (formData.type === 'PDF' && formData.pages <= 0) {
      errors.pages = 'Pages must be greater than 0';
    }
    if ((formData.type === 'Video' || formData.type === 'Audio') && !formData.duration.trim()) {
      errors.duration = 'Duration is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'PDF',
      subject: '',
      price: 0,
      originalPrice: 0,
      author: '',
      isPremium: false,
      thumbnailUrl: '',
      pages: 0,
      duration: '',
      fileSize: ''
    });
    setFormErrors({});
  };

  const handleCreate = async () => {
    if (!validateForm()) return;

    try {
      const newMaterial = {
        ...formData,
        id: Date.now(),
        rating: 0,
        reviews: 0,
        status: 'published' as const,
        downloads: 0,
        createdAt: new Date().toISOString().split('T')[0]
      };

      await studyMaterialsAPI.create(newMaterial);
      await fetchMaterials();
      setShowCreateModal(false);
      resetForm();
    } catch (error) {
      console.error('Error creating material:', error);
    }
  };

  const handleEdit = async () => {
    if (!validateForm() || !selectedMaterial) return;

    try {
      const updatedMaterial = {
        ...selectedMaterial,
        ...formData
      };

      await studyMaterialsAPI.update(selectedMaterial.id, updatedMaterial);
      await fetchMaterials();
      setShowEditModal(false);
      setSelectedMaterial(null);
      resetForm();
    } catch (error) {
      console.error('Error updating material:', error);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this material?')) return;

    try {
      await studyMaterialsAPI.delete(id);
      await fetchMaterials();
    } catch (error) {
      console.error('Error deleting material:', error);
    }
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (material: StudyMaterial) => {
    setSelectedMaterial(material);
    setFormData({
      title: material.title,
      description: material.description,
      type: material.type,
      subject: material.subject,
      price: material.price,
      originalPrice: material.originalPrice || 0,
      author: material.author,
      isPremium: material.isPremium,
      thumbnailUrl: material.thumbnailUrl,
      pages: material.pages || 0,
      duration: material.duration || '',
      fileSize: material.fileSize
    });
    setShowEditModal(true);
  };

  const openViewModal = (material: StudyMaterial) => {
    setSelectedMaterial(material);
    setShowViewModal(true);
  };

  const getTypeIcon = (type: string) => {
    const iconClass = "h-8 w-8 p-2 rounded-lg text-white";
    switch (type) {
      case 'PDF':
        return <FileText className={`${iconClass} bg-red-500`} />;
      case 'Video':
        return <Video className={`${iconClass} bg-blue-500`} />;
      case 'Audio':
        return <HeadphonesIcon className={`${iconClass} bg-green-500`} />;
      default:
        return <BookOpen className={`${iconClass} bg-gray-500`} />;
    }
  };

  const handlePurchase = (materialId: number) => {
    console.log('Purchasing material:', materialId);
    alert('Purchase functionality would be implemented here!');
  };

  // Filter logic
  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = subjectFilter === 'all' || material.subject === subjectFilter;
    const matchesType = typeFilter === 'all' || material.type === typeFilter;

    let matchesPrice = true;
    if (priceFilter === 'free') matchesPrice = material.price === 0;
    else if (priceFilter === 'under-200') matchesPrice = material.price < 200;
    else if (priceFilter === '200-500') matchesPrice = material.price >= 200 && material.price <= 500;
    else if (priceFilter === 'above-500') matchesPrice = material.price > 500;

    return matchesSearch && matchesSubject && matchesType && matchesPrice;
  });

  return (
    <div className="space-y-6">
      {/* Temporary Admin Test Panel */}
      <AdminTest />

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Study Materials</h1>
          <p className="text-gray-600 mt-1">Premium study materials for UPSC preparation</p>
          {/* Debug info - remove in production */}
          <div className="mt-2 text-xs text-gray-500">
            Current user: {user?.name || 'Not logged in'} | Role: {user?.role || 'None'} |
            Admin check: {user?.role === 'admin' ? 'TRUE' : 'FALSE'}
          </div>
        </div>
        <div className="flex items-center space-x-4 mt-4 sm:mt-0">
          {/* Quick login buttons for testing */}
          {!user && (
            <div className="flex space-x-2">
              <button
                onClick={async () => {
                  await login('<EMAIL>', 'password123');
                }}
                className="bg-gray-600 text-white px-3 py-1 rounded text-sm"
              >
                Login as Student
              </button>
              <button
                onClick={async () => {
                  await login('<EMAIL>', 'admin123');
                }}
                className="bg-red-600 text-white px-3 py-1 rounded text-sm"
              >
                Login as Admin
              </button>
            </div>
          )}

          {user?.role === 'admin' && (
            <button
              onClick={openCreateModal}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Material</span>
            </button>
          )}
          <div className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5 text-gray-600" />
            <span className="text-sm text-gray-600">0 items in cart</span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <select
            value={subjectFilter}
            onChange={(e) => setSubjectFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Subjects</option>
            {subjects.slice(1).map(subject => (
              <option key={subject} value={subject}>{subject}</option>
            ))}
          </select>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            {types.slice(1).map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>

          <select
            value={priceFilter}
            onChange={(e) => setPriceFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Prices</option>
            <option value="free">Free</option>
            <option value="under-200">Under ₹200</option>
            <option value="200-500">₹200 - ₹500</option>
            <option value="above-500">Above ₹500</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading study materials...</p>
        </div>
      )}

      {/* Materials Grid */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMaterials.map((material) => (
            <div key={material.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <img
                  src={material.thumbnailUrl}
                  alt={material.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4">
                  {getTypeIcon(material.type)}
                </div>
                {material.isPremium && (
                  <div className="absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    Premium
                  </div>
                )}
              </div>

              <div className="p-6">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{material.title}</h3>
                </div>

                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{material.description}</p>

                <div className="flex items-center space-x-2 mb-3">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium text-gray-700 ml-1">{material.rating}</span>
                  </div>
                  <span className="text-sm text-gray-500">({material.reviews} reviews)</span>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>By {material.author}</span>
                  <span className="bg-gray-100 px-2 py-1 rounded text-xs">{material.subject}</span>
                </div>

                <div className="flex items-center text-sm text-gray-500 mb-4">
                  {material.type === 'PDF' && (
                    <span className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {material.pages} pages
                    </span>
                  )}
                  {(material.type === 'Video' || material.type === 'Audio') && (
                    <span className="flex items-center">
                      <Play className="h-4 w-4 mr-1" />
                      {material.duration}
                    </span>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl font-bold text-green-600">₹{material.price}</span>
                    {material.originalPrice && material.originalPrice > material.price && (
                      <span className="text-sm text-gray-500 line-through">₹{material.originalPrice}</span>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => openViewModal(material)}
                      className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>

                    {user?.role === 'admin' ? (
                      <>
                        <button
                          onClick={() => openEditModal(material)}
                          className="p-2 text-blue-600 hover:text-blue-900 hover:bg-blue-100 rounded-lg transition-colors"
                          title="Edit Material"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(material.id)}
                          className="p-2 text-red-600 hover:text-red-900 hover:bg-red-100 rounded-lg transition-colors"
                          title="Delete Material"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => handlePurchase(material.id)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center space-x-1"
                      >
                        <ShoppingCart className="h-4 w-4" />
                        <span>Buy Now</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!loading && filteredMaterials.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No materials found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(showCreateModal || showEditModal) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  {showCreateModal ? 'Add New Material' : 'Edit Material'}
                </h2>
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    setShowEditModal(false);
                    resetForm();
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title *
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.title ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="Enter material title"
                    />
                    {formErrors.title && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.title}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Type *
                    </label>
                    <select
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value as 'PDF' | 'Video' | 'Audio' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="PDF">PDF</option>
                      <option value="Video">Video</option>
                      <option value="Audio">Audio</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.description ? 'border-red-500' : 'border-gray-300'
                      }`}
                    placeholder="Enter material description"
                  />
                  {formErrors.description && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject *
                    </label>
                    <select
                      value={formData.subject}
                      onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.subject ? 'border-red-500' : 'border-gray-300'
                        }`}
                    >
                      <option value="">Select Subject</option>
                      <option value="Current Affairs">Current Affairs</option>
                      <option value="Polity">Polity</option>
                      <option value="Economics">Economics</option>
                      <option value="Geography">Geography</option>
                      <option value="History">History</option>
                      <option value="Science & Technology">Science & Technology</option>
                    </select>
                    {formErrors.subject && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.subject}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Author *
                    </label>
                    <input
                      type="text"
                      value={formData.author}
                      onChange={(e) => setFormData({ ...formData, author: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.author ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="Enter author name"
                    />
                    {formErrors.author && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.author}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price (₹) *
                    </label>
                    <input
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.price ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="0"
                      min="0"
                    />
                    {formErrors.price && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.price}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Original Price (₹)
                    </label>
                    <input
                      type="number"
                      value={formData.originalPrice}
                      onChange={(e) => setFormData({ ...formData, originalPrice: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="0"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      File Size *
                    </label>
                    <input
                      type="text"
                      value={formData.fileSize}
                      onChange={(e) => setFormData({ ...formData, fileSize: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.fileSize ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="e.g., 15.2 MB"
                    />
                    {formErrors.fileSize && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.fileSize}</p>
                    )}
                  </div>
                </div>

                {formData.type === 'PDF' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Number of Pages *
                    </label>
                    <input
                      type="number"
                      value={formData.pages}
                      onChange={(e) => setFormData({ ...formData, pages: Number(e.target.value) })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.pages ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="0"
                      min="1"
                    />
                    {formErrors.pages && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.pages}</p>
                    )}
                  </div>
                )}

                {(formData.type === 'Video' || formData.type === 'Audio') && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration *
                    </label>
                    <input
                      type="text"
                      value={formData.duration}
                      onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.duration ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="e.g., 24 hours"
                    />
                    {formErrors.duration && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.duration}</p>
                    )}
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thumbnail URL *
                  </label>
                  <input
                    type="url"
                    value={formData.thumbnailUrl}
                    onChange={(e) => setFormData({ ...formData, thumbnailUrl: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${formErrors.thumbnailUrl ? 'border-red-500' : 'border-gray-300'
                      }`}
                    placeholder="https://example.com/image.jpg"
                  />
                  {formErrors.thumbnailUrl && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.thumbnailUrl}</p>
                  )}
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isPremium"
                    checked={formData.isPremium}
                    onChange={(e) => setFormData({ ...formData, isPremium: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isPremium" className="ml-2 block text-sm text-gray-700">
                    Premium Content
                  </label>
                </div>

                <div className="flex justify-end space-x-4 pt-6 border-t">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateModal(false);
                      setShowEditModal(false);
                      resetForm();
                    }}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={showCreateModal ? handleCreate : handleEdit}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                  >
                    <Save className="h-4 w-4" />
                    <span>{showCreateModal ? 'Create' : 'Update'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && selectedMaterial && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Material Details</h2>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    setSelectedMaterial(null);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                <div className="relative">
                  <img
                    src={selectedMaterial.thumbnailUrl}
                    alt={selectedMaterial.title}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <div className="absolute top-4 left-4">
                    {getTypeIcon(selectedMaterial.type)}
                  </div>
                  {selectedMaterial.isPremium && (
                    <div className="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Premium
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{selectedMaterial.title}</h3>
                  <p className="text-gray-600 mb-4">{selectedMaterial.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Subject</span>
                    <p className="text-gray-900">{selectedMaterial.subject}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Author</span>
                    <p className="text-gray-900">{selectedMaterial.author}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Type</span>
                    <p className="text-gray-900">{selectedMaterial.type}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">File Size</span>
                    <p className="text-gray-900">{selectedMaterial.fileSize}</p>
                  </div>
                  {selectedMaterial.type === 'PDF' && selectedMaterial.pages && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Pages</span>
                      <p className="text-gray-900">{selectedMaterial.pages}</p>
                    </div>
                  )}
                  {(selectedMaterial.type === 'Video' || selectedMaterial.type === 'Audio') && selectedMaterial.duration && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Duration</span>
                      <p className="text-gray-900">{selectedMaterial.duration}</p>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-500 fill-current" />
                    <span className="font-medium text-gray-900">{selectedMaterial.rating}</span>
                    <span className="text-gray-500">({selectedMaterial.reviews} reviews)</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedMaterial.downloads} downloads
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <span className="text-3xl font-bold text-green-600">₹{selectedMaterial.price}</span>
                    {selectedMaterial.originalPrice && selectedMaterial.originalPrice > selectedMaterial.price && (
                      <span className="text-lg text-gray-500 line-through">₹{selectedMaterial.originalPrice}</span>
                    )}
                  </div>

                  <div className="flex space-x-3">
                    {user?.role === 'admin' ? (
                      <>
                        <button
                          onClick={() => {
                            setShowViewModal(false);
                            openEditModal(selectedMaterial);
                          }}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                        >
                          <Edit className="h-4 w-4" />
                          <span>Edit</span>
                        </button>
                        <button
                          onClick={() => {
                            setShowViewModal(false);
                            handleDelete(selectedMaterial.id);
                          }}
                          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span>Delete</span>
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => handlePurchase(selectedMaterial.id)}
                        className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                      >
                        <ShoppingCart className="h-4 w-4" />
                        <span>Buy Now</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default StudyMaterials;
