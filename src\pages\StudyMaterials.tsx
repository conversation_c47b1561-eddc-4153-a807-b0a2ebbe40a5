import React, { useState } from 'react';
import { 
  BookOpen, 
  Download, 
  Play, 
  Search, 
  Filter, 
  Star, 
  ShoppingCart,
  Eye,
  FileText,
  Video,
  HeadphonesIcon
} from 'lucide-react';

function StudyMaterials() {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [priceFilter, setPriceFilter] = useState('all');

  const materials = [
    {
      id: 1,
      title: 'Current Affairs March 2024',
      description: 'Comprehensive current affairs compilation with 500+ questions and detailed explanations',
      type: 'PDF',
      subject: 'Current Affairs',
      price: 299,
      originalPrice: 399,
      rating: 4.8,
      reviews: 245,
      pages: 150,
      author: 'Dr. <PERSON><PERSON>',
      isPremium: true,
      thumbnailUrl: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg'
    },
    {
      id: 2,
      title: 'Indian Polity Video Series',
      description: 'Complete video course covering entire Indian Polity syllabus with animations',
      type: 'Video',
      subject: 'Polity',
      price: 599,
      originalPrice: 799,
      rating: 4.9,
      reviews: 189,
      duration: '24 hours',
      author: 'Prof. <PERSON><PERSON><PERSON>',
      isPremium: true,
      thumbnailUrl: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg'
    },
    {
      id: 3,
      title: 'Economics Mind Maps',
      description: 'Visual mind maps for all economics topics with memory techniques',
      type: 'PDF',
      subject: 'Economics',
      price: 199,
      originalPrice: 299,
      rating: 4.6,
      reviews: 156,
      pages: 80,
      author: 'CA Priya Mehta',
      isPremium: false,
      thumbnailUrl: 'https://images.pexels.com/photos/590016/pexels-photo-590016.jpeg'
    },
    {
      id: 4,
      title: 'Geography Audio Lectures',
      description: 'Complete geography syllabus in audio format for mobile learning',
      type: 'Audio',
      subject: 'Geography',
      price: 349,
      originalPrice: 449,
      rating: 4.7,
      reviews: 203,
      duration: '18 hours',
      author: 'Dr. Vikram Singh',
      isPremium: true,
      thumbnailUrl: 'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg'
    },
    {
      id: 5,
      title: 'History Timeline Charts',
      description: 'Chronological charts covering Ancient, Medieval, and Modern Indian History',
      type: 'PDF',
      subject: 'History',
      price: 149,
      originalPrice: 199,
      rating: 4.5,
      reviews: 134,
      pages: 45,
      author: 'Prof. Meera Gupta',
      isPremium: false,
      thumbnailUrl: 'https://images.pexels.com/photos/1164572/pexels-photo-1164572.jpeg'
    },
    {
      id: 6,
      title: 'Science & Technology Video Course',
      description: 'Latest developments in science and technology with regular updates',
      type: 'Video',
      subject: 'Science & Technology',
      price: 449,
      originalPrice: 599,
      rating: 4.8,
      reviews: 178,
      duration: '16 hours',
      author: 'Dr. Rohit Sharma',
      isPremium: true,
      thumbnailUrl: 'https://images.pexels.com/photos/2280571/pexels-photo-2280571.jpeg'
    }
  ];

  const subjects = ['all', 'Current Affairs', 'Polity', 'Economics', 'Geography', 'History', 'Science & Technology'];
  const types = ['all', 'PDF', 'Video', 'Audio'];
  const priceRanges = ['all', 'free', 'under-200', '200-500', 'above-500'];

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = filter === 'all' || material.subject === filter;
    const matchesType = filter === 'all' || material.type === filter;
    
    let matchesPrice = true;
    if (priceFilter === 'free') matchesPrice = material.price === 0;
    else if (priceFilter === 'under-200') matchesPrice = material.price < 200;
    else if (priceFilter === '200-500') matchesPrice = material.price >= 200 && material.price <= 500;
    else if (priceFilter === 'above-500') matchesPrice = material.price > 500;
    
    return matchesSearch && matchesSubject && matchesType && matchesPrice;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Video': return <Video className="h-5 w-5 text-red-600" />;
      case 'Audio': return <HeadphonesIcon className="h-5 w-5 text-purple-600" />;
      default: return <FileText className="h-5 w-5 text-blue-600" />;
    }
  };

  const handlePurchase = (materialId: number) => {
    // Simulate purchase
    alert('Redirecting to payment gateway...');
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Study Materials</h1>
          <p className="text-gray-600 mt-1">Premium study materials for UPSC preparation</p>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <ShoppingCart className="h-5 w-5 text-gray-600" />
          <span className="text-sm text-gray-600">0 items in cart</span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search materials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Subjects</option>
            {subjects.slice(1).map(subject => (
              <option key={subject} value={subject}>{subject}</option>
            ))}
          </select>

          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            {types.slice(1).map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>

          <select
            value={priceFilter}
            onChange={(e) => setPriceFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Prices</option>
            <option value="free">Free</option>
            <option value="under-200">Under ₹200</option>
            <option value="200-500">₹200 - ₹500</option>
            <option value="above-500">Above ₹500</option>
          </select>
        </div>
      </div>

      {/* Materials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMaterials.map((material) => (
          <div key={material.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="relative">
              <img
                src={material.thumbnailUrl}
                alt={material.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-4 left-4">
                {getTypeIcon(material.type)}
              </div>
              {material.isPremium && (
                <div className="absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  Premium
                </div>
              )}
            </div>
            
            <div className="p-6">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{material.title}</h3>
              </div>
              
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{material.description}</p>
              
              <div className="flex items-center space-x-2 mb-3">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium text-gray-700 ml-1">{material.rating}</span>
                </div>
                <span className="text-sm text-gray-500">({material.reviews} reviews)</span>
              </div>
              
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>By {material.author}</span>
                <span className="bg-gray-100 px-2 py-1 rounded text-xs">{material.subject}</span>
              </div>
              
              <div className="flex items-center text-sm text-gray-500 mb-4">
                {material.type === 'PDF' && (
                  <span className="flex items-center">
                    <FileText className="h-4 w-4 mr-1" />
                    {material.pages} pages
                  </span>
                )}
                {(material.type === 'Video' || material.type === 'Audio') && (
                  <span className="flex items-center">
                    <Play className="h-4 w-4 mr-1" />
                    {material.duration}
                  </span>
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl font-bold text-green-600">₹{material.price}</span>
                  {material.originalPrice > material.price && (
                    <span className="text-sm text-gray-500 line-through">₹{material.originalPrice}</span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                    <Eye className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handlePurchase(material.id)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center space-x-1"
                  >
                    <ShoppingCart className="h-4 w-4" />
                    <span>Buy Now</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredMaterials.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No materials found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
}

export default StudyMaterials;