// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  password?: string;
  phone?: string;
  role: 'student' | 'admin';
  subscription?: 'free' | 'premium';
  joinedAt: string;
  lastActive?: string;
  status?: 'active' | 'inactive';
  examsCompleted?: number;
  avgScore?: number;
  avatar?: string;
}

// Study Material Types
export interface StudyMaterial {
  id: number;
  title: string;
  description: string;
  type: 'PDF' | 'Video' | 'Audio';
  subject: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviews: number;
  pages?: number;
  duration?: string;
  author: string;
  isPremium: boolean;
  thumbnailUrl: string;
  status: 'published' | 'draft';
  downloads: number;
  createdAt: string;
  fileSize: string;
}

// Exam Types
export interface Exam {
  id: number;
  title: string;
  description: string;
  type: string;
  duration: number;
  questions: number;
  participants: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  status: 'published' | 'draft' | 'upcoming';
  startDate: string;
  startTime: string;
  createdAt: string;
  scheduledAt: string;
  creator: string;
}

// Question Types
export interface Question {
  id: number;
  examId: number;
  question: string;
  options: string[];
  correct: number;
  explanation?: string;
}

// Testimonial Types
export interface Testimonial {
  id: number;
  name: string;
  rank: string;
  image: string;
  quote: string;
  featured: boolean;
}

// Pricing Plan Types
export interface PricingPlan {
  id: number;
  name: string;
  price: string;
  period: string;
  originalPrice?: string;
  features: string[];
  popular: boolean;
  active: boolean;
}

// Notification Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
  audience?: 'all' | 'premium' | 'free';
  status?: 'sent' | 'scheduled' | 'draft';
  sentAt?: string;
  recipients?: number;
  openRate?: number;
}

// Admin Stats Types
export interface AdminStat {
  id: number;
  label: string;
  value: string;
  change: string;
  icon: string;
  color: string;
}

// Chart Data Types
export interface ChartData {
  month: string;
  users?: number;
  score?: number;
}

// Dashboard Types
export interface UpcomingExam {
  id: number;
  title: string;
  date: string;
  time: string;
}

export interface RecentMaterial {
  id: number;
  title: string;
  type: string;
  price: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  error?: string;
}

export interface RegisterResponse {
  success: boolean;
  user?: User;
  error?: string;
}

// Context Types
export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

export interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  clearNotifications: () => void;
  unreadCount: number;
}

// Filter Types
export interface MaterialFilters {
  search: string;
  subject: string;
  type: string;
  priceRange: string;
}

export interface ExamFilters {
  search: string;
  type: string;
  difficulty: string;
  status: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface ContactForm {
  name: string;
  email: string;
  phone: string;
  message: string;
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams {
  query: string;
  filters?: Record<string, any>;
}

// Export all types as a namespace for easier imports
export * from './index';
