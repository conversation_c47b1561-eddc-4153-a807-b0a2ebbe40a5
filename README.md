# 🎯 Brainstorm UPSC Academy - SaaS Platform

**India's #1 Online UPSC Preparation Platform**

A comprehensive Software-as-a-Service (SaaS) platform designed specifically for UPSC (Union Public Service Commission) aspirants. This platform provides mock tests, premium study materials, progress tracking, and expert mentorship to help students crack the UPSC Civil Services Examination.

![Platform Preview](https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg)

## 🌟 Features

### 🎓 For Students
- **Weekly Mock Tests** - UPSC-pattern tests with detailed analysis and performance insights
- **Premium Study Materials** - Curated content by top educators and subject matter experts
- **Progress Tracking** - AI-powered analytics to track preparation and improvement
- **Expert Mentorship** - One-on-one guidance from successful IAS officers and educators
- **Interactive Dashboard** - Comprehensive overview of performance, study hours, and goals
- **Study Materials Store** - Purchase premium PDFs, videos, and audio content
- **Exam Room** - Simulated exam environment for realistic test experience
- **Profile Management** - Track personal progress and subscription details

### 👨‍💼 For Administrators
- **Admin Dashboard** - Platform performance and user activity overview
- **User Management** - Manage student accounts and subscriptions
- **Exam Management** - Create, edit, and manage mock tests
- **Content Management** - Upload and manage study materials
- **Notification System** - Send announcements and updates to users

### 💰 Subscription Plans
- **Basic Plan** - ₹999/month - Weekly mock tests, basic materials, performance analytics
- **Premium Plan** - ₹2,999/year - All basic features + video lectures, expert mentorship, mobile app
- **Elite Plan** - ₹4,999/year - All premium features + 1-on-1 mentoring, interview prep, lifetime access

## 🛠️ Technology Stack

### Frontend
- **React 18.3.1** - Modern React with hooks and functional components
- **TypeScript** - Type-safe JavaScript for better development experience
- **Tailwind CSS 3.4.1** - Utility-first CSS framework for rapid UI development
- **React Router DOM 6.22.0** - Client-side routing and navigation
- **Lucide React 0.344.0** - Beautiful, customizable icons
- **Recharts 2.12.0** - Composable charting library for data visualization
- **Date-fns 3.3.1** - Modern JavaScript date utility library

### Build Tools & Development
- **Vite 5.4.2** - Fast build tool and development server
- **ESLint** - Code linting and quality assurance
- **PostCSS & Autoprefixer** - CSS processing and vendor prefixing
- **TypeScript ESLint** - TypeScript-specific linting rules

### Architecture Patterns
- **Context API** - State management for authentication and notifications
- **Protected Routes** - Role-based access control
- **Component-based Architecture** - Reusable and maintainable UI components
- **Responsive Design** - Mobile-first approach with Tailwind CSS

## 📁 Project Structure

```
upsc-saas-platform/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── Layout.tsx     # Main application layout
│   │   ├── Navbar.tsx     # Navigation bar
│   │   ├── Sidebar.tsx    # Side navigation
│   │   ├── ProtectedRoute.tsx # Route protection
│   │   └── NotificationDropdown.tsx
│   ├── contexts/          # React Context providers
│   │   ├── AuthContext.tsx # Authentication state
│   │   └── NotificationContext.tsx
│   ├── pages/             # Page components
│   │   ├── LandingPage.tsx # Marketing landing page
│   │   ├── Dashboard.tsx   # Student dashboard
│   │   ├── Exams.tsx      # Mock tests listing
│   │   ├── ExamRoom.tsx   # Test taking interface
│   │   ├── StudyMaterials.tsx # Materials marketplace
│   │   ├── Profile.tsx    # User profile
│   │   ├── Login.tsx      # Authentication
│   │   ├── Register.tsx   # User registration
│   │   └── admin/         # Admin panel pages
│   │       ├── AdminDashboard.tsx
│   │       ├── AdminUsers.tsx
│   │       ├── AdminExams.tsx
│   │       ├── AdminMaterials.tsx
│   │       └── AdminNotifications.tsx
│   ├── App.tsx            # Main application component
│   ├── main.tsx           # Application entry point
│   ├── index.css          # Global styles
│   └── vite-env.d.ts      # Vite type definitions
├── index.html             # HTML template with SEO meta tags
├── package.json           # Dependencies and scripts
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── vite.config.ts         # Vite build configuration
└── README.md              # Project documentation
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd upsc-saas-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` to view the application

### Build for Production

```bash
npm run build
npm run preview
```

## 🎯 Usage

### For Students
1. **Registration** - Create an account on the landing page
2. **Login** - Access your dashboard with credentials
3. **Take Mock Tests** - Navigate to Exams section for practice tests
4. **Study Materials** - Browse and purchase premium content
5. **Track Progress** - Monitor performance through dashboard analytics

### For Administrators
1. **Admin Access** - Login with admin credentials (<EMAIL>)
2. **Manage Users** - View and manage student accounts
3. **Create Exams** - Add new mock tests and questions
4. **Upload Materials** - Add study content to the marketplace
5. **Send Notifications** - Communicate with users platform-wide

### Demo Credentials
- **Student**: <EMAIL> (any password)
- **Admin**: <EMAIL> (any password)

## 🔗 Key Routes

- `/` - Landing page with marketing content
- `/login` - User authentication
- `/register` - New user registration
- `/app` - Protected application routes:
  - `/app` - Student dashboard
  - `/app/exams` - Mock tests listing
  - `/app/exam/:id` - Individual exam interface
  - `/app/materials` - Study materials marketplace
  - `/app/profile` - User profile management
  - `/app/admin/*` - Admin panel (admin only)

## 🎨 Design Features

- **Modern UI/UX** - Clean, professional design with Tailwind CSS
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Dark/Light Theme** - Consistent color scheme throughout the application
- **Interactive Components** - Hover effects, transitions, and animations
- **Accessibility** - WCAG compliant design patterns
- **SEO Optimized** - Meta tags, structured data, and semantic HTML

## 📊 Key Metrics & Analytics

- **50,000+ Active Students** - Large user base
- **2,500+ Success Stories** - Proven track record
- **95% Success Rate** - High effectiveness
- **24/7 Support** - Continuous assistance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Email**: <EMAIL>
- **Phone**: +91 98765 43210
- **Address**: New Delhi, India

---

**Built with ❤️ for UPSC Aspirants**
