import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import {
  Calendar,
  Clock,
  User,
  Tag,
  Search,
  Filter,
  TrendingUp,
  BookOpen,
  Star,
  ArrowRight,
  Eye,
  MessageCircle,
  Share2,
  Bookmark,
  ChevronRight,
  Globe,
  Award,
  Target,
  Lightbulb,
  PenTool,
  Users,
  ChevronRight
} from 'lucide-react';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  category: string;
  tags: string[];
  publishedAt: string;
  readTime: string;
  views: number;
  comments: number;
  featured: boolean;
  image: string;
  seoTitle: string;
  metaDescription: string;
}

interface BlogCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  postCount: number;
}

function Blog() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [featuredPosts, setFeaturedPosts] = useState<BlogPost[]>([]);
  const [allBlogPosts, setAllBlogPosts] = useState<BlogPost[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // SEO-optimized blog categories
  const blogCategories: BlogCategory[] = [
    {
      id: 'upsc-strategy',
      name: 'UPSC Strategy',
      description: 'Proven strategies and tips for UPSC preparation',
      icon: Target,
      color: 'bg-blue-500',
      postCount: 45
    },
    {
      id: 'current-affairs',
      name: 'Current Affairs',
      description: 'Latest news analysis and current affairs updates',
      icon: Globe,
      color: 'bg-green-500',
      postCount: 120
    },
    {
      id: 'study-tips',
      name: 'Study Tips',
      description: 'Effective study techniques and time management',
      icon: Lightbulb,
      color: 'bg-purple-500',
      postCount: 38
    },
    {
      id: 'success-stories',
      name: 'Success Stories',
      description: 'Inspiring journeys of UPSC toppers',
      icon: Award,
      color: 'bg-orange-500',
      postCount: 25
    },
    {
      id: 'exam-analysis',
      name: 'Exam Analysis',
      description: 'Detailed analysis of UPSC exam patterns',
      icon: TrendingUp,
      color: 'bg-red-500',
      postCount: 32
    },
    {
      id: 'interview-guidance',
      name: 'Interview Guidance',
      description: 'Tips and strategies for UPSC interview',
      icon: Users,
      color: 'bg-indigo-500',
      postCount: 18
    }
  ];

  // Sample blog posts
  useEffect(() => {
    const samplePosts: BlogPost[] = [
      {
        id: '1',
        title: 'Complete UPSC Preparation Strategy for 2024: A Comprehensive Guide',
        excerpt: 'Master the art of UPSC preparation with our detailed strategy guide covering all three stages of the exam.',
        content: 'Full article content here...',
        author: {
          name: 'Dr. Rajesh Kumar',
          avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',
          bio: 'Former IAS Officer & UPSC Mentor'
        },
        category: 'UPSC Strategy',
        tags: ['UPSC', 'Strategy', 'Preparation', '2024'],
        publishedAt: '2024-03-15',
        readTime: '12 min read',
        views: 15420,
        comments: 89,
        featured: true,
        image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
        seoTitle: 'UPSC Preparation Strategy 2024 - Complete Guide by IAS Officer',
        metaDescription: 'Learn proven UPSC preparation strategies from former IAS officer. Complete guide covering Prelims, Mains & Interview preparation for 2024.'
      },
      {
        id: '2',
        title: 'Current Affairs Weekly Digest: March 2024 - Key Events for UPSC',
        excerpt: 'Stay updated with the most important current affairs events of March 2024 relevant for UPSC examination.',
        content: 'Full article content here...',
        author: {
          name: 'Priya Sharma',
          avatar: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
          bio: 'Current Affairs Expert & UPSC Trainer'
        },
        category: 'Current Affairs',
        tags: ['Current Affairs', 'March 2024', 'UPSC', 'News'],
        publishedAt: '2024-03-20',
        readTime: '8 min read',
        views: 8750,
        comments: 45,
        featured: true,
        image: 'https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg',
        seoTitle: 'Current Affairs March 2024 - UPSC Weekly Digest',
        metaDescription: 'Important current affairs events of March 2024 for UPSC preparation. Weekly digest with analysis and key points.'
      },
      {
        id: '3',
        title: 'How I Cracked UPSC in My First Attempt: Success Story',
        excerpt: 'Read the inspiring journey of Ankit Verma who secured AIR 23 in UPSC CSE 2023 in his first attempt.',
        content: 'Full article content here...',
        author: {
          name: 'Ankit Verma',
          avatar: 'https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg',
          bio: 'IAS Officer, AIR 23 UPSC CSE 2023'
        },
        category: 'Success Stories',
        tags: ['Success Story', 'First Attempt', 'AIR 23', 'Motivation'],
        publishedAt: '2024-03-18',
        readTime: '15 min read',
        views: 12300,
        comments: 156,
        featured: true,
        image: 'https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg',
        seoTitle: 'UPSC Success Story - AIR 23 First Attempt Strategy',
        metaDescription: 'Inspiring UPSC success story of AIR 23 Ankit Verma. Learn his first attempt strategy, study plan and preparation tips.'
      }
    ];

    setBlogPosts(samplePosts);
    setFeaturedPosts(samplePosts.filter(post => post.featured));

    // Extended blog posts for sidebar
    const allPosts: BlogPost[] = [
      ...samplePosts,
      {
        id: '4',
        title: 'UPSC Mains Answer Writing Strategy',
        excerpt: 'Master the art of answer writing for UPSC Mains with proven techniques and practice methods.',
        content: 'Full article content here...',
        author: {
          name: 'Dr. Meera Singh',
          avatar: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
          bio: 'Former IAS Officer & UPSC Trainer'
        },
        category: 'UPSC Strategy',
        tags: ['Mains', 'Answer Writing', 'Strategy'],
        publishedAt: '2024-03-17',
        readTime: '10 min read',
        views: 9200,
        comments: 67,
        featured: false,
        image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
        seoTitle: 'UPSC Mains Answer Writing Strategy - Expert Guide',
        metaDescription: 'Learn proven answer writing techniques for UPSC Mains examination from expert trainers.'
      },
      {
        id: '5',
        title: 'Time Management Tips for UPSC Preparation',
        excerpt: 'Effective time management strategies to balance UPSC preparation with work and personal life.',
        content: 'Full article content here...',
        author: {
          name: 'Rahul Sharma',
          avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',
          bio: 'UPSC Mentor & Time Management Expert'
        },
        category: 'Study Tips',
        tags: ['Time Management', 'Productivity', 'Balance'],
        publishedAt: '2024-03-16',
        readTime: '7 min read',
        views: 6800,
        comments: 45,
        featured: false,
        image: 'https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg',
        seoTitle: 'Time Management Tips for UPSC Preparation',
        metaDescription: 'Effective time management strategies for UPSC aspirants to balance study and life.'
      },
      {
        id: '6',
        title: 'UPSC Interview: Common Questions and Answers',
        excerpt: 'Prepare for UPSC personality test with commonly asked questions and expert-recommended answers.',
        content: 'Full article content here...',
        author: {
          name: 'Kavita Patel',
          avatar: 'https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg',
          bio: 'IAS Officer & Interview Panel Member'
        },
        category: 'Interview Guidance',
        tags: ['Interview', 'Personality Test', 'Questions'],
        publishedAt: '2024-03-15',
        readTime: '12 min read',
        views: 11500,
        comments: 89,
        featured: false,
        image: 'https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg',
        seoTitle: 'UPSC Interview Questions and Answers - Expert Guide',
        metaDescription: 'Common UPSC interview questions with expert-recommended answers and preparation tips.'
      },
      {
        id: '7',
        title: 'February 2024 Current Affairs Digest',
        excerpt: 'Important current affairs events of February 2024 for UPSC preparation with analysis.',
        content: 'Full article content here...',
        author: {
          name: 'Amit Kumar',
          avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',
          bio: 'Current Affairs Expert'
        },
        category: 'Current Affairs',
        tags: ['February 2024', 'Current Affairs', 'Monthly'],
        publishedAt: '2024-03-01',
        readTime: '15 min read',
        views: 8900,
        comments: 56,
        featured: false,
        image: 'https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg',
        seoTitle: 'February 2024 Current Affairs for UPSC',
        metaDescription: 'Important current affairs events of February 2024 with detailed analysis for UPSC preparation.'
      },
      {
        id: '8',
        title: 'UPSC Prelims 2024: Expected Cut-off Analysis',
        excerpt: 'Detailed analysis of expected cut-off marks for UPSC Prelims 2024 based on exam difficulty.',
        content: 'Full article content here...',
        author: {
          name: 'Dr. Suresh Gupta',
          avatar: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
          bio: 'UPSC Analyst & Former Examiner'
        },
        category: 'Exam Analysis',
        tags: ['Prelims 2024', 'Cut-off', 'Analysis'],
        publishedAt: '2024-02-28',
        readTime: '8 min read',
        views: 13200,
        comments: 78,
        featured: false,
        image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
        seoTitle: 'UPSC Prelims 2024 Expected Cut-off Analysis',
        metaDescription: 'Expert analysis of expected cut-off marks for UPSC Prelims 2024 examination.'
      }
    ];

    setAllBlogPosts(allPosts);
  }, []);

  const filteredPosts = allBlogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category.toLowerCase().replace(' ', '-') === selectedCategory;
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <>
      {/* SEO Meta Tags */}
      <Helmet>
        <title>UPSC Blog - Expert Tips, Current Affairs & Success Stories | Brainstorm UPSC</title>
        <meta name="description" content="Read expert UPSC preparation tips, current affairs analysis, success stories and study strategies. Latest blog posts by IAS officers and UPSC mentors." />
        <meta name="keywords" content="UPSC blog, IAS preparation tips, current affairs analysis, UPSC success stories, civil services blog, UPSC study tips, exam strategy" />
        <meta property="og:title" content="UPSC Blog - Expert Tips & Success Stories" />
        <meta property="og:description" content="Expert UPSC preparation tips, current affairs analysis and inspiring success stories from IAS officers." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://brainstormupsc.com/blog" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="UPSC Blog - Expert Tips & Success Stories" />
        <meta name="twitter:description" content="Expert UPSC preparation tips and inspiring success stories." />
        <link rel="canonical" href="https://brainstormupsc.com/blog" />

        {/* Structured Data for SEO */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Blog",
            "name": "Brainstorm UPSC Blog",
            "description": "Expert UPSC preparation tips, current affairs and success stories",
            "url": "https://brainstormupsc.com/blog",
            "publisher": {
              "@type": "Organization",
              "name": "Brainstorm UPSC",
              "logo": {
                "@type": "ImageObject",
                "url": "https://brainstormupsc.com/logo.png"
              }
            },
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": "https://brainstormupsc.com/blog"
            }
          })}
        </script>
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <Link to="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Brainstorm UPSC</h1>
                  <p className="text-sm text-gray-600">Blog</p>
                </div>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link to="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</Link>
                <Link to="/quiz" className="text-gray-600 hover:text-blue-600 transition-colors">Quiz</Link>
                <Link to="/login" className="text-blue-600 hover:text-blue-700 font-medium">Login</Link>
                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Join Now
                </Link>
              </nav>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <PenTool className="h-4 w-4" />
                <span>Expert UPSC Insights</span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                UPSC Success
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"> Insights</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Get expert tips, current affairs analysis, and inspiring success stories from IAS officers and UPSC mentors to accelerate your preparation.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-blue-600 mb-2">200+</div>
                <div className="text-gray-600">Expert Articles</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-green-600 mb-2">50+</div>
                <div className="text-gray-600">IAS Contributors</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-purple-600 mb-2">100K+</div>
                <div className="text-gray-600">Monthly Readers</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-orange-600 mb-2">Daily</div>
                <div className="text-gray-600">Fresh Content</div>
              </div>
            </div>
          </div>
        </section>

        {/* Search and Filter */}
        <section className="py-8 bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-center space-x-4">
                <Filter className="h-5 w-5 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {blogCategories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                <button
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="lg:hidden bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <span>All Articles</span>
                  <ChevronRight className={`h-4 w-4 transform transition-transform ${sidebarOpen ? 'rotate-90' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content with Sidebar */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex gap-8">
            {/* Sidebar */}
            <aside className={`w-80 flex-shrink-0 ${sidebarOpen ? 'block' : 'hidden lg:block'}`}>
              <div className="sticky top-8">
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                    <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                      <PenTool className="h-5 w-5" />
                      <span>All Articles ({filteredPosts.length})</span>
                    </h3>
                  </div>

                  <div className="max-h-96 overflow-y-auto">
                    {filteredPosts.length > 0 ? (
                      <div className="divide-y divide-gray-100">
                        {filteredPosts.map((post) => (
                          <div key={post.id} className="p-4 hover:bg-gray-50 transition-colors cursor-pointer group">
                            <div className="flex items-start space-x-3">
                              <img
                                src={post.author.avatar}
                                alt={post.author.name}
                                className="w-8 h-8 rounded-full object-cover flex-shrink-0"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                                    {post.title}
                                  </h4>
                                  {post.featured && (
                                    <span className="bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full text-xs font-medium">
                                      Featured
                                    </span>
                                  )}
                                </div>
                                <p className="text-xs text-gray-500 mb-2">{post.category}</p>
                                <div className="flex items-center space-x-3 text-xs text-gray-500">
                                  <span className="flex items-center space-x-1">
                                    <Calendar className="h-3 w-3" />
                                    <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                                  </span>
                                  <span className="flex items-center space-x-1">
                                    <Clock className="h-3 w-3" />
                                    <span>{post.readTime}</span>
                                  </span>
                                  <span className="flex items-center space-x-1">
                                    <Eye className="h-3 w-3" />
                                    <span>{post.views.toLocaleString()}</span>
                                  </span>
                                </div>
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {post.tags.slice(0, 2).map((tag, index) => (
                                    <span key={index} className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-md text-xs">
                                      #{tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                            <div className="mt-2 flex items-center justify-between">
                              <span className="text-xs text-gray-500 flex items-center space-x-1">
                                <MessageCircle className="h-3 w-3" />
                                <span>{post.comments} comments</span>
                              </span>
                              <button className="text-blue-600 hover:text-blue-700 text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                                Read More →
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-6 text-center text-gray-500">
                        <PenTool className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p>No articles found</p>
                        <p className="text-sm">Try adjusting your search or filter</p>
                      </div>
                    )}
                  </div>

                  {filteredPosts.length > 0 && (
                    <div className="bg-gray-50 px-6 py-3 border-t">
                      <button className="w-full text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All Articles →
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </aside>

            {/* Main Content */}
            <main className="flex-1 min-w-0">

              {/* Featured Posts */}
              <section className="py-16">
                <div>
                  <div className="text-center mb-12">
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                      Featured Articles
                    </h2>
                    <p className="text-xl text-gray-600">
                      Must-read articles handpicked by our expert editorial team
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                    {featuredPosts.slice(0, 2).map((post, index) => (
                      <article key={post.id} className={`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow ${index === 0 ? 'lg:col-span-2' : ''}`}>
                        <div className={`grid ${index === 0 ? 'lg:grid-cols-2' : 'grid-cols-1'} gap-0`}>
                          <div className="relative">
                            <img
                              src={post.image}
                              alt={post.title}
                              className={`w-full object-cover ${index === 0 ? 'h-64 lg:h-full' : 'h-48'}`}
                            />
                            <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                              Featured
                            </div>
                          </div>

                          <div className="p-8">
                            <div className="flex items-center space-x-4 mb-4">
                              <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                                {post.category}
                              </span>
                              <span className="text-gray-500 text-sm flex items-center space-x-1">
                                <Calendar className="h-4 w-4" />
                                <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                              </span>
                            </div>

                            <h3 className={`font-bold text-gray-900 mb-3 ${index === 0 ? 'text-2xl lg:text-3xl' : 'text-xl'}`}>
                              {post.title}
                            </h3>
                            <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>

                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center space-x-3">
                                <img
                                  src={post.author.avatar}
                                  alt={post.author.name}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                                <div>
                                  <div className="font-medium text-gray-900 text-sm">{post.author.name}</div>
                                  <div className="text-gray-500 text-xs">{post.author.bio}</div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <span className="flex items-center space-x-1">
                                  <Clock className="h-4 w-4" />
                                  <span>{post.readTime}</span>
                                </span>
                                <span className="flex items-center space-x-1">
                                  <Eye className="h-4 w-4" />
                                  <span>{post.views.toLocaleString()}</span>
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center justify-between">
                              <div className="flex flex-wrap gap-2">
                                {post.tags.slice(0, 3).map((tag, tagIndex) => (
                                  <span key={tagIndex} className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-xs">
                                    #{tag}
                                  </span>
                                ))}
                              </div>
                              <button className="text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1">
                                <span>Read More</span>
                                <ArrowRight className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </article>
                    ))}
                  </div>
                </div>
              </section>

              {/* Blog Categories */}
              <section className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="text-center mb-12">
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                      Explore Categories
                    </h2>
                    <p className="text-xl text-gray-600">
                      Find articles tailored to your UPSC preparation needs
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {blogCategories.map((category) => (
                      <div key={category.id} className="group bg-white rounded-xl border border-gray-200 p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
                        <div className={`w-12 h-12 ${category.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                          <category.icon className="h-6 w-6 text-white" />
                        </div>

                        <h3 className="text-xl font-semibold text-gray-900 mb-2">{category.name}</h3>
                        <p className="text-gray-600 mb-4">{category.description}</p>

                        <div className="flex items-center justify-between mb-4">
                          <span className="text-sm text-gray-500">{category.postCount} articles</span>
                          <button className="text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1">
                            <span>View All</span>
                            <ChevronRight className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Recent Posts */}
              <section className="py-16 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="text-center mb-12">
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                      Latest Articles
                    </h2>
                    <p className="text-xl text-gray-600">
                      Stay updated with our newest insights and tips
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {filteredPosts.map((post) => (
                      <article key={post.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
                        <div className="relative">
                          <img
                            src={post.image}
                            alt={post.title}
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                            {post.category}
                          </div>
                        </div>

                        <div className="p-6">
                          <div className="flex items-center space-x-4 mb-3">
                            <span className="text-gray-500 text-sm flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                            </span>
                            <span className="text-gray-500 text-sm flex items-center space-x-1">
                              <Clock className="h-4 w-4" />
                              <span>{post.readTime}</span>
                            </span>
                          </div>

                          <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">{post.title}</h3>
                          <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>

                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-2">
                              <img
                                src={post.author.avatar}
                                alt={post.author.name}
                                className="w-6 h-6 rounded-full object-cover"
                              />
                              <span className="text-sm font-medium text-gray-700">{post.author.name}</span>
                            </div>
                            <div className="flex items-center space-x-3 text-sm text-gray-500">
                              <span className="flex items-center space-x-1">
                                <Eye className="h-4 w-4" />
                                <span>{post.views.toLocaleString()}</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <MessageCircle className="h-4 w-4" />
                                <span>{post.comments}</span>
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {post.tags.slice(0, 2).map((tag, index) => (
                                <span key={index} className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-xs">
                                  #{tag}
                                </span>
                              ))}
                            </div>
                            <div className="flex items-center space-x-2">
                              <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                                <Bookmark className="h-4 w-4" />
                              </button>
                              <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                                <Share2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </article>
                    ))}
                  </div>

                  <div className="text-center mt-12">
                    <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                      Load More Articles
                    </button>
                  </div>
                </div>
              </section>
            </main>
          </div>
        </div>

        {/* Newsletter Signup */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Never Miss an Update
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Subscribe to our newsletter and get the latest UPSC insights, current affairs, and success tips delivered to your inbox
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              />
              <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">B</span>
                  </div>
                  <span className="text-xl font-bold">Brainstorm UPSC Blog</span>
                </div>
                <p className="text-gray-400 mb-4">
                  Your trusted source for UPSC preparation insights, current affairs analysis, and success stories from India's top civil servants.
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">Facebook</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">Twitter</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">LinkedIn</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">YouTube</a>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><Link to="/" className="hover:text-white transition-colors">Home</Link></li>
                  <li><Link to="/quiz" className="hover:text-white transition-colors">Quiz</Link></li>
                  <li><Link to="/login" className="hover:text-white transition-colors">Login</Link></li>
                  <li><Link to="/register" className="hover:text-white transition-colors">Register</Link></li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Categories</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#" className="hover:text-white transition-colors">UPSC Strategy</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Current Affairs</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Success Stories</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Study Tips</a></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; 2024 Brainstorm UPSC. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

export default Blog;
