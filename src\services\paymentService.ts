import { StudyMaterial, User } from '../types';

// Razorpay configuration
const RAZORPAY_BACKEND_URL = 'http://localhost:5000';

// Extend Window interface for Razorpay
declare global {
  interface Window {
    Razorpay: any;
  }
}

export interface PaymentOptions {
  material: StudyMaterial;
  user: User;
  onSuccess: (paymentData: any) => void;
  onFailure: (error: any) => void;
}

export interface CreateOrderResponse {
  id: string;
  amount: number;
  currency: string;
  receipt: string;
  key_id: string;
  notes: any;
}

export interface PaymentVerificationResponse {
  status: 'success' | 'failure' | 'error';
  message: string;
  paymentDetails?: {
    paymentId: string;
    orderId: string;
    amount: number;
    currency: string;
    status: string;
    method: string;
    materialId: string;
    userId: string;
  };
}

class PaymentService {
  private isRazorpayLoaded = false;

  // Load Razorpay script dynamically
  private async loadRazorpayScript(): Promise<boolean> {
    if (this.isRazorpayLoaded) return true;

    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        this.isRazorpayLoaded = true;
        resolve(true);
      };
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  }

  // Create order on backend
  private async createOrder(material: StudyMaterial, user: User): Promise<CreateOrderResponse> {
    try {
      const response = await fetch(`${RAZORPAY_BACKEND_URL}/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: material.price,
          materialId: material.id,
          materialTitle: material.title,
          userId: user.id,
          userEmail: user.email,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create order');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Verify payment on backend
  private async verifyPayment(paymentData: any, materialId: number, userId: number): Promise<PaymentVerificationResponse> {
    try {
      const response = await fetch(`${RAZORPAY_BACKEND_URL}/verify-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          razorpay_order_id: paymentData.razorpay_order_id,
          razorpay_payment_id: paymentData.razorpay_payment_id,
          razorpay_signature: paymentData.razorpay_signature,
          materialId,
          userId,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  }

  // Main payment method
  async initiatePayment({ material, user, onSuccess, onFailure }: PaymentOptions): Promise<void> {
    try {
      // Load Razorpay script
      const isLoaded = await this.loadRazorpayScript();
      if (!isLoaded) {
        throw new Error('Failed to load Razorpay script');
      }

      // Create order
      const orderData = await this.createOrder(material, user);

      // Razorpay options
      const options = {
        key: orderData.key_id,
        amount: orderData.amount,
        currency: orderData.currency,
        name: 'UPSC Study Platform',
        description: `Purchase: ${material.title}`,
        image: '/logo.png', // Add your logo here
        order_id: orderData.id,
        handler: async (response: any) => {
          try {
            // Verify payment
            const verificationResult = await this.verifyPayment(
              response,
              material.id,
              user.id
            );

            if (verificationResult.status === 'success') {
              onSuccess({
                ...verificationResult,
                material,
                user,
              });
            } else {
              onFailure(new Error(verificationResult.message));
            }
          } catch (error) {
            onFailure(error);
          }
        },
        prefill: {
          name: user.name,
          email: user.email,
          contact: user.phone || '',
        },
        notes: {
          materialId: material.id.toString(),
          materialTitle: material.title,
          userId: user.id.toString(),
        },
        theme: {
          color: '#2563eb', // Blue color matching your theme
        },
        modal: {
          ondismiss: () => {
            onFailure(new Error('Payment cancelled by user'));
          },
        },
      };

      // Open Razorpay checkout
      const rzp = new window.Razorpay(options);
      rzp.open();
    } catch (error) {
      console.error('Payment initiation error:', error);
      onFailure(error);
    }
  }

  // Get payment status
  async getPaymentStatus(paymentId: string): Promise<any> {
    try {
      const response = await fetch(`${RAZORPAY_BACKEND_URL}/payment-status/${paymentId}`);
      return await response.json();
    } catch (error) {
      console.error('Error fetching payment status:', error);
      throw error;
    }
  }

  // Format amount for display
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  }
}

export const paymentService = new PaymentService();
