import { StudyMaterial, User } from '../types';

// Payment gateway configurations
const RAZORPAY_BACKEND_URL = 'http://localhost:5000';
const CASHFREE_BACKEND_URL = 'http://localhost:5001';

// Extend Window interface for payment gateways
declare global {
  interface Window {
    Razorpay: any;
    Cashfree: any;
  }
}

export type PaymentGateway = 'razorpay' | 'cashfree';

export interface PaymentOptions {
  material: StudyMaterial;
  user: User;
  onSuccess: (paymentData: any) => void;
  onFailure: (error: any) => void;
  preferredGateway?: PaymentGateway;
}

export interface CreateOrderResponse {
  id: string;
  amount: number;
  currency: string;
  receipt?: string;
  key_id?: string; // Razorpay
  client_id?: string; // Cashfree
  notes?: any;
  order_id?: string; // Cashfree
  payment_session_id?: string; // Cashfree
}

export interface PaymentVerificationResponse {
  status: 'success' | 'failure' | 'error';
  message: string;
  paymentDetails?: {
    paymentId: string;
    orderId: string;
    amount: number;
    currency: string;
    status: string;
    method: string;
    materialId: string;
    userId: string;
  };
}

class PaymentService {
  private isRazorpayLoaded = false;
  private isCashfreeLoaded = false;

  // Load Razorpay script dynamically
  private async loadRazorpayScript(): Promise<boolean> {
    if (this.isRazorpayLoaded) return true;

    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        this.isRazorpayLoaded = true;
        resolve(true);
      };
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  }

  // Load Cashfree script dynamically
  private async loadCashfreeScript(): Promise<boolean> {
    if (this.isCashfreeLoaded) return true;

    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://sdk.cashfree.com/js/v3/cashfree.js';
      script.onload = () => {
        this.isCashfreeLoaded = true;
        resolve(true);
      };
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  }

  // Check if payment gateway is available
  private async checkGatewayHealth(gateway: PaymentGateway): Promise<boolean> {
    try {
      const url = gateway === 'razorpay' ?
        `${RAZORPAY_BACKEND_URL}/health` :
        `${CASHFREE_BACKEND_URL}/health`;

      const response = await fetch(url, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      return response.ok;
    } catch (error) {
      console.warn(`${gateway} gateway health check failed:`, error);
      return false;
    }
  }

  // Create Razorpay order
  private async createRazorpayOrder(material: StudyMaterial, user: User): Promise<CreateOrderResponse> {
    try {
      const response = await fetch(`${RAZORPAY_BACKEND_URL}/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: material.price,
          materialId: material.id,
          materialTitle: material.title,
          userId: user.id,
          userEmail: user.email,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create Razorpay order');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating Razorpay order:', error);
      throw error;
    }
  }

  // Create Cashfree order
  private async createCashfreeOrder(material: StudyMaterial, user: User): Promise<CreateOrderResponse> {
    try {
      const response = await fetch(`${CASHFREE_BACKEND_URL}/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: material.price,
          materialId: material.id,
          materialTitle: material.title,
          userId: user.id,
          userEmail: user.email,
          userName: user.name,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create Cashfree order');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating Cashfree order:', error);
      throw error;
    }
  }

  // Verify Razorpay payment
  private async verifyRazorpayPayment(paymentData: any, materialId: number, userId: number): Promise<PaymentVerificationResponse> {
    try {
      const response = await fetch(`${RAZORPAY_BACKEND_URL}/verify-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          razorpay_order_id: paymentData.razorpay_order_id,
          razorpay_payment_id: paymentData.razorpay_payment_id,
          razorpay_signature: paymentData.razorpay_signature,
          materialId,
          userId,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error verifying Razorpay payment:', error);
      throw error;
    }
  }

  // Verify Cashfree payment
  private async verifyCashfreePayment(orderId: string): Promise<PaymentVerificationResponse> {
    try {
      const response = await fetch(`${CASHFREE_BACKEND_URL}/verify-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error verifying Cashfree payment:', error);
      throw error;
    }
  }

  // Main payment method with fallback logic
  async initiatePayment({ material, user, onSuccess, onFailure, preferredGateway = 'razorpay' }: PaymentOptions): Promise<void> {
    console.log(`🚀 Initiating payment with preferred gateway: ${preferredGateway}`);

    // Try preferred gateway first
    const success = await this.tryPaymentGateway(preferredGateway, { material, user, onSuccess, onFailure });

    if (!success) {
      // Fallback to alternative gateway
      const fallbackGateway = preferredGateway === 'razorpay' ? 'cashfree' : 'razorpay';
      console.log(`⚠️ ${preferredGateway} failed, trying fallback: ${fallbackGateway}`);

      const fallbackSuccess = await this.tryPaymentGateway(fallbackGateway, { material, user, onSuccess, onFailure });

      if (!fallbackSuccess) {
        onFailure(new Error('Both payment gateways are currently unavailable. Please try again later.'));
      }
    }
  }

  // Try specific payment gateway
  private async tryPaymentGateway(gateway: PaymentGateway, { material, user, onSuccess, onFailure }: PaymentOptions): Promise<boolean> {
    try {
      // Check gateway health first
      const isHealthy = await this.checkGatewayHealth(gateway);
      if (!isHealthy) {
        console.warn(`${gateway} gateway is not healthy`);
        return false;
      }

      if (gateway === 'razorpay') {
        return await this.processRazorpayPayment({ material, user, onSuccess, onFailure });
      } else {
        return await this.processCashfreePayment({ material, user, onSuccess, onFailure });
      }
    } catch (error) {
      console.error(`${gateway} payment failed:`, error);
      return false;
    }
  }

  // Process Razorpay payment
  private async processRazorpayPayment({ material, user, onSuccess, onFailure }: PaymentOptions): Promise<boolean> {
    try {
      // Load Razorpay script
      const isLoaded = await this.loadRazorpayScript();
      if (!isLoaded) {
        throw new Error('Failed to load Razorpay script');
      }

      // Create order
      const orderData = await this.createRazorpayOrder(material, user);

      // Razorpay options
      const options = {
        key: orderData.key_id,
        amount: orderData.amount,
        currency: orderData.currency,
        name: 'UPSC Study Platform',
        description: `Purchase: ${material.title}`,
        image: '/logo.png',
        order_id: orderData.id,
        handler: async (response: any) => {
          try {
            const verificationResult = await this.verifyRazorpayPayment(
              response,
              material.id,
              Number(user.id)
            );

            if (verificationResult.status === 'success') {
              onSuccess({
                ...verificationResult,
                material,
                user,
                gateway: 'razorpay'
              });
            } else {
              onFailure(new Error(verificationResult.message));
            }
          } catch (error) {
            onFailure(error);
          }
        },
        prefill: {
          name: user.name,
          email: user.email,
          contact: user.phone || '',
        },
        notes: {
          materialId: material.id.toString(),
          materialTitle: material.title,
          userId: user.id.toString(),
        },
        theme: {
          color: '#2563eb',
        },
        modal: {
          ondismiss: () => {
            onFailure(new Error('Payment cancelled by user'));
          },
        },
      };

      // Open Razorpay checkout
      const rzp = new window.Razorpay(options);
      rzp.open();
      return true;
    } catch (error) {
      console.error('Razorpay payment error:', error);
      return false;
    }
  }

  // Process Cashfree payment
  private async processCashfreePayment({ material, user, onSuccess, onFailure }: PaymentOptions): Promise<boolean> {
    try {
      // Load Cashfree script
      const isLoaded = await this.loadCashfreeScript();
      if (!isLoaded) {
        throw new Error('Failed to load Cashfree script');
      }

      // Create order
      const orderData = await this.createCashfreeOrder(material, user);

      // Initialize Cashfree
      const cashfree = window.Cashfree({
        mode: 'sandbox' // Change to 'production' for live
      });

      // Cashfree checkout options
      const checkoutOptions = {
        paymentSessionId: orderData.payment_session_id,
        redirectTarget: '_modal'
      };

      // Open Cashfree checkout
      cashfree.checkout(checkoutOptions).then((result: any) => {
        if (result.error) {
          console.error('Cashfree payment error:', result.error);
          onFailure(new Error(result.error.message || 'Payment failed'));
          return;
        }

        if (result.redirect) {
          console.log('Cashfree payment redirect:', result.redirect);
        }

        if (result.paymentDetails) {
          // Verify payment
          this.verifyCashfreePayment(orderData.order_id || orderData.id)
            .then((verificationResult) => {
              if (verificationResult.status === 'success') {
                onSuccess({
                  ...verificationResult,
                  material,
                  user,
                  gateway: 'cashfree'
                });
              } else {
                onFailure(new Error(verificationResult.message));
              }
            })
            .catch((error) => {
              onFailure(error);
            });
        }
      });

      return true;
    } catch (error) {
      console.error('Cashfree payment error:', error);
      return false;
    }
  }

  // Get payment status (supports both gateways)
  async getPaymentStatus(paymentId: string, gateway: PaymentGateway = 'razorpay'): Promise<any> {
    try {
      const url = gateway === 'razorpay' ?
        `${RAZORPAY_BACKEND_URL}/payment-status/${paymentId}` :
        `${CASHFREE_BACKEND_URL}/payment-status/${paymentId}`;

      const response = await fetch(url);
      return await response.json();
    } catch (error) {
      console.error(`Error fetching ${gateway} payment status:`, error);
      throw error;
    }
  }

  // Get available payment gateways
  async getAvailableGateways(): Promise<PaymentGateway[]> {
    const gateways: PaymentGateway[] = [];

    if (await this.checkGatewayHealth('razorpay')) {
      gateways.push('razorpay');
    }

    if (await this.checkGatewayHealth('cashfree')) {
      gateways.push('cashfree');
    }

    return gateways;
  }

  // Format amount for display
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  }
}

export const paymentService = new PaymentService();
