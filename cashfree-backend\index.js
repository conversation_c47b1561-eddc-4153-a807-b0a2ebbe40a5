import express from 'express';
import axios from 'axios';
import dotenv from 'dotenv';
import crypto from 'crypto';
import cors from 'cors';
import bodyParser from 'body-parser';

dotenv.config();

// Validate environment variables
if (!process.env.CF_CLIENT_ID || !process.env.CF_CLIENT_SECRET) {
  console.error('❌ CF_CLIENT_ID and CF_CLIENT_SECRET must be set in .env file');
  console.error('📝 Please check your .env file and add your Cashfree API keys');
  process.exit(1);
}

if (process.env.CF_CLIENT_SECRET === 'YOUR_ACTUAL_CLIENT_SECRET_HERE' ||
  process.env.CF_CLIENT_SECRET === 'your_client_secret') {
  console.error('❌ Please replace YOUR_ACTUAL_CLIENT_SECRET_HERE with your actual Cashfree client secret');
  console.error('🔗 Get your keys from: https://merchant.cashfree.com/merchants/login');
  process.exit(1);
}

console.log('✅ Cashfree Client ID:', process.env.CF_CLIENT_ID);
//console.log('✅ Cashfree Client Secret:', process.env.CF_CLIENT_SECRET.substring(0, 8) + '...');

const app = express();
app.use(cors());
app.use(bodyParser.json());

const { CF_CLIENT_ID, CF_CLIENT_SECRET } = process.env;

// Cashfree API Base URL (Sandbox)
const CASHFREE_API_BASE = 'https://sandbox.cashfree.com/pg';

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Cashfree backend is running',
    cashfree_configured: !!CF_CLIENT_ID && !!CF_CLIENT_SECRET
  });
});

// Create Cashfree order for study material purchase
app.post('/create-order', async (req, res) => {
  try {
    const { amount, materialId, materialTitle, userId, userEmail, userName } = req.body;

    // Validate required fields
    if (!amount || !materialId || !userId) {
      return res.status(400).json({
        error: 'Missing required fields: amount, materialId, userId'
      });
    }

    // Generate unique order ID
    const orderId = `order_${materialId}_${userId}_${Date.now()}`;

    const payload = {
      order_id: orderId,
      order_amount: amount,
      order_currency: 'INR',
      customer_details: {
        customer_id: userId.toString(),
        customer_name: userName || 'Student',
        customer_email: userEmail || '<EMAIL>',
        customer_phone: '**********' // Default phone for demo
      },
      order_meta: {
        return_url: `${req.headers.origin || 'http://localhost:5173'}/payment-success`,
        notify_url: `http://localhost:5001/webhook`,
        payment_methods: 'cc,dc,nb,upi,paylater,emi'
      },
      order_note: `Purchase: ${materialTitle || 'Study Material'}`,
      order_tags: {
        materialId: materialId.toString(),
        materialTitle: materialTitle || 'Study Material',
        userId: userId.toString(),
        purchaseType: 'study_material'
      }
    };

    console.log('Creating Cashfree order:', {
      orderId,
      amount,
      materialId,
      userId,
      payload: JSON.stringify(payload, null, 2)
    });

    const response = await axios.post(
      `${CASHFREE_API_BASE}/orders`,
      payload,
      {
        headers: {
          'x-client-id': CF_CLIENT_ID,
          'x-client-secret': CF_CLIENT_SECRET,
          'Content-Type': 'application/json',
          'x-api-version': '2023-08-01'
        }
      }
    );

    console.log('✅ Cashfree order created successfully:', {
      order_id: response.data.order_id,
      payment_session_id: response.data.payment_session_id,
      order_status: response.data.order_status
    });

    res.json({
      ...response.data,
      client_id: CF_CLIENT_ID // Send client_id for frontend
    });
  } catch (err) {
    console.error('❌ Cashfree order creation error:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message
    });

    // Send detailed error response
    const errorResponse = {
      error: 'Unable to create Cashfree order',
      details: err.response?.data || err.message,
      gateway: 'cashfree'
    };

    res.status(err.response?.status || 500).json(errorResponse);
  }
});

// Verify payment status
app.post('/verify-payment', async (req, res) => {
  try {
    const { orderId } = req.body;

    if (!orderId) {
      return res.status(400).json({
        error: 'Missing required field: orderId'
      });
    }

    // Fetch order details from Cashfree
    const response = await axios.get(
      `${CASHFREE_API_BASE}/orders/${orderId}`,
      {
        headers: {
          'x-client-id': CF_CLIENT_ID,
          'x-client-secret': CF_CLIENT_SECRET,
          'x-api-version': '2023-08-01'
        }
      }
    );

    const order = response.data;

    console.log('Payment verification result:', {
      orderId,
      status: order.order_status,
      amount: order.order_amount
    });

    if (order.order_status === 'PAID') {
      res.json({
        status: 'success',
        message: 'Payment verified successfully',
        paymentDetails: {
          orderId: order.order_id,
          amount: order.order_amount,
          currency: order.order_currency,
          status: order.order_status,
          paymentMethod: order.payment_method || 'unknown',
          materialId: order.order_tags?.materialId,
          userId: order.order_tags?.userId
        }
      });
    } else {
      res.status(400).json({
        status: 'failure',
        message: `Payment not completed. Status: ${order.order_status}`
      });
    }
  } catch (err) {
    console.error('Payment verification error:', err.response?.data || err.message);
    res.status(500).json({
      status: 'error',
      message: 'Payment verification failed',
      details: err.response?.data || err.message
    });
  }
});

// Get payment status
app.get('/payment-status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    const response = await axios.get(
      `${CASHFREE_API_BASE}/orders/${orderId}`,
      {
        headers: {
          'x-client-id': CF_CLIENT_ID,
          'x-client-secret': CF_CLIENT_SECRET,
          'x-api-version': '2023-08-01'
        }
      }
    );

    const order = response.data;

    res.json({
      status: 'success',
      order: {
        id: order.order_id,
        amount: order.order_amount,
        currency: order.order_currency,
        status: order.order_status,
        created_at: order.created_at
      }
    });
  } catch (err) {
    console.error('Error fetching payment status:', err.response?.data || err.message);
    res.status(500).json({
      status: 'error',
      message: 'Unable to fetch payment status'
    });
  }
});

// Webhook verification
app.post('/webhook', (req, res) => {
  try {
    const payload = JSON.stringify(req.body);
    const timestamp = req.headers['x-webhook-timestamp'];
    const signature = req.headers['x-webhook-signature'];

    const expected = crypto
      .createHmac('sha256', CF_CLIENT_SECRET)
      .update(timestamp + payload)
      .digest('base64');

    if (expected !== signature) {
      return res.status(400).send('Invalid signature');
    }

    console.log('Cashfree webhook received and verified:', req.body);

    // Here you would typically:
    // 1. Update order status in database
    // 2. Grant access to purchased material
    // 3. Send confirmation email

    res.status(200).send('OK');
  } catch (err) {
    console.error('Webhook processing error:', err);
    res.status(500).send('Webhook processing failed');
  }
});

// Start server
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`🚀 Cashfree backend running at http://localhost:${PORT}`);
});
