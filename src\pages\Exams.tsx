import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Clock, Users, Calendar, Filter, Search, FileText, Award } from 'lucide-react';

function Exams() {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const exams = [
    {
      id: 1,
      title: 'UPSC Prelims Mock Test 16',
      description: 'Comprehensive test covering all subjects with detailed analysis',
      duration: 120,
      questions: 100,
      participants: 1250,
      difficulty: 'Hard',
      status: 'upcoming',
      startDate: '2024-03-20',
      startTime: '10:00 AM',
      type: 'Mock Test'
    },
    {
      id: 2,
      title: 'Current Affairs Weekly Test',
      description: 'Latest current affairs covering March 2024',
      duration: 60,
      questions: 50,
      participants: 890,
      difficulty: 'Medium',
      status: 'upcoming',
      startDate: '2024-03-22',
      startTime: '2:00 PM',
      type: 'Current Affairs'
    },
    {
      id: 3,
      title: 'History Chapter Test - Ancient India',
      description: 'Focused test on Ancient Indian History',
      duration: 45,
      questions: 30,
      participants: 650,
      difficulty: 'Easy',
      status: 'completed',
      startDate: '2024-03-15',
      startTime: '11:00 AM',
      type: 'Subject Test',
      score: 85
    },
    {
      id: 4,
      title: 'Geography Full Length Test',
      description: 'Complete geography syllabus coverage',
      duration: 90,
      questions: 75,
      participants: 1100,
      difficulty: 'Hard',
      status: 'live',
      startDate: '2024-03-18',
      startTime: '9:00 AM',
      type: 'Full Length'
    }
  ];

  const filteredExams = exams.filter(exam => {
    const matchesFilter = filter === 'all' || exam.status === filter;
    const matchesSearch = exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         exam.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getStatusBadge = (status: string, score?: number) => {
    switch (status) {
      case 'upcoming':
        return <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">Upcoming</span>;
      case 'live':
        return <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Live</span>;
      case 'completed':
        return (
          <div className="flex items-center space-x-2">
            <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">Completed</span>
            {score && (
              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium flex items-center">
                <Award className="h-3 w-3 mr-1" />
                {score}%
              </span>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-600 bg-green-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'Hard': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Exams</h1>
          <p className="text-gray-600 mt-1">Take practice tests and track your progress</p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex items-center space-x-4">
            <Filter className="h-5 w-5 text-gray-600" />
            <div className="flex space-x-2">
              {['all', 'upcoming', 'live', 'completed'].map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium capitalize transition-colors ${
                    filter === status
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {status}
                </button>
              ))}
            </div>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search exams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Exams Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredExams.map((exam) => (
          <div key={exam.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{exam.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exam.difficulty)}`}>
                      {exam.difficulty}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">{exam.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {exam.duration} min
                    </div>
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {exam.questions} questions
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {exam.participants} participants
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {getStatusBadge(exam.status, exam.score)}
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1" />
                    {exam.startDate} at {exam.startTime}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  {exam.status === 'upcoming' && (
                    <Link
                      to={`/exam/${exam.id}`}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      Start Exam
                    </Link>
                  )}
                  {exam.status === 'live' && (
                    <Link
                      to={`/exam/${exam.id}`}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors animate-pulse"
                    >
                      Join Now
                    </Link>
                  )}
                  {exam.status === 'completed' && (
                    <button className="bg-gray-100 text-gray-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                      View Results
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredExams.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No exams found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
}

export default Exams;