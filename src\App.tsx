import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import Layout from './components/Layout';
import LandingPage from './pages/LandingPage';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import Exams from './pages/Exams';
import ExamRoom from './pages/ExamRoom';
import StudyMaterials from './pages/StudyMaterials';
import Profile from './pages/Profile';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminUsers from './pages/admin/AdminUsers';
import AdminExams from './pages/admin/AdminExams';
import AdminMaterials from './pages/StudyMaterials';
import AdminNotifications from './pages/admin/AdminNotifications';
import ProtectedRoute from './components/ProtectedRoute';
import './index.css';

function App() {
  return (
    <AuthProvider>
      <NotificationProvider>
        <Router>
          <Routes>
            <Route path="/" element={<LandingPage />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/app" element={<Layout />}>
              <Route index element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
              <Route path="exams" element={<ProtectedRoute><Exams /></ProtectedRoute>} />
              <Route path="exam/:id" element={<ProtectedRoute><ExamRoom /></ProtectedRoute>} />
              <Route path="materials" element={<ProtectedRoute><StudyMaterials /></ProtectedRoute>} />
              <Route path="profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
              <Route path="admin" element={<ProtectedRoute adminOnly><AdminDashboard /></ProtectedRoute>} />
              <Route path="admin/users" element={<ProtectedRoute adminOnly><AdminUsers /></ProtectedRoute>} />
              <Route path="admin/exams" element={<ProtectedRoute adminOnly><AdminExams /></ProtectedRoute>} />
              <Route path="admin/materials" element={<ProtectedRoute adminOnly><AdminMaterials /></ProtectedRoute>} />
              <Route path="admin/notifications" element={<ProtectedRoute adminOnly><AdminNotifications /></ProtectedRoute>} />
            </Route>
          </Routes>
        </Router>
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;