const API_BASE_URL = 'http://localhost:3001';

// Generic API functions
const api = {
  get: async (endpoint: string) => {
    const response = await fetch(`${API_BASE_URL}${endpoint}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  post: async (endpoint: string, data: any) => {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  put: async (endpoint: string, data: any) => {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },

  delete: async (endpoint: string) => {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  },
};

// User API
export const userAPI = {
  getAll: () => api.get('/users'),
  getById: (id: string) => api.get(`/users/${id}`),
  getByEmail: (email: string) => api.get(`/users?email=${email}`),
  create: (user: any) => api.post('/users', user),
  update: (id: string, user: any) => api.put(`/users/${id}`, user),
  delete: (id: string) => api.delete(`/users/${id}`),
};

// Study Materials API
export const studyMaterialsAPI = {
  getAll: () => api.get('/studyMaterials'),
  getById: (id: number) => api.get(`/studyMaterials/${id}`),
  getBySubject: (subject: string) => api.get(`/studyMaterials?subject=${subject}`),
  getByType: (type: string) => api.get(`/studyMaterials?type=${type}`),
  create: (material: any) => api.post('/studyMaterials', material),
  update: (id: number, material: any) => api.put(`/studyMaterials/${id}`, material),
  delete: (id: number) => api.delete(`/studyMaterials/${id}`),
};

// Exams API
export const examsAPI = {
  getAll: () => api.get('/exams'),
  getById: (id: number) => api.get(`/exams/${id}`),
  getByStatus: (status: string) => api.get(`/exams?status=${status}`),
  create: (exam: any) => api.post('/exams', exam),
  update: (id: number, exam: any) => api.put(`/exams/${id}`, exam),
  delete: (id: number) => api.delete(`/exams/${id}`),
};

// Questions API
export const questionsAPI = {
  getAll: () => api.get('/questions'),
  getByExamId: (examId: number) => api.get(`/questions?examId=${examId}`),
  getById: (id: number) => api.get(`/questions/${id}`),
  create: (question: any) => api.post('/questions', question),
  update: (id: number, question: any) => api.put(`/questions/${id}`, question),
  delete: (id: number) => api.delete(`/questions/${id}`),
};

// Testimonials API
export const testimonialsAPI = {
  getAll: () => api.get('/testimonials'),
  getFeatured: () => api.get('/testimonials?featured=true'),
  getById: (id: number) => api.get(`/testimonials/${id}`),
  create: (testimonial: any) => api.post('/testimonials', testimonial),
  update: (id: number, testimonial: any) => api.put(`/testimonials/${id}`, testimonial),
  delete: (id: number) => api.delete(`/testimonials/${id}`),
};

// Pricing Plans API
export const pricingPlansAPI = {
  getAll: () => api.get('/pricingPlans'),
  getActive: () => api.get('/pricingPlans?active=true'),
  getById: (id: number) => api.get(`/pricingPlans/${id}`),
  create: (plan: any) => api.post('/pricingPlans', plan),
  update: (id: number, plan: any) => api.put(`/pricingPlans/${id}`, plan),
  delete: (id: number) => api.delete(`/pricingPlans/${id}`),
};

// Notifications API
export const notificationsAPI = {
  getAll: () => api.get('/notifications'),
  getUnread: () => api.get('/notifications?read=false'),
  getById: (id: string) => api.get(`/notifications/${id}`),
  create: (notification: any) => api.post('/notifications', notification),
  update: (id: string, notification: any) => api.put(`/notifications/${id}`, notification),
  delete: (id: string) => api.delete(`/notifications/${id}`),
  markAsRead: (id: string) => {
    return api.put(`/notifications/${id}`, { read: true });
  },
};

// Admin Stats API
export const adminStatsAPI = {
  getAll: () => api.get('/adminStats'),
  getUserGrowthData: () => api.get('/userGrowthData'),
};

// Dashboard API
export const dashboardAPI = {
  getProgressData: () => api.get('/progressData'),
  getUpcomingExams: () => api.get('/upcomingExams'),
  getRecentMaterials: () => api.get('/recentMaterials'),
};

// Authentication API (simulated)
export const authAPI = {
  login: async (email: string, password: string) => {
    try {
      const users = await userAPI.getByEmail(email);
      const user = users.find((u: any) => u.email === email && u.password === password);
      if (user) {
        // Update last active
        await userAPI.update(user.id, { ...user, lastActive: new Date().toISOString().split('T')[0] });
        return { success: true, user };
      }
      return { success: false, error: 'Invalid credentials' };
    } catch (error) {
      return { success: false, error: 'Login failed' };
    }
  },

  register: async (name: string, email: string, password: string) => {
    try {
      // Check if user already exists
      const existingUsers = await userAPI.getByEmail(email);
      if (existingUsers.length > 0) {
        return { success: false, error: 'User already exists' };
      }

      const newUser = {
        id: Date.now().toString(),
        name,
        email,
        password,
        role: 'student',
        subscription: 'free',
        joinedAt: new Date().toISOString().split('T')[0],
        lastActive: new Date().toISOString().split('T')[0],
        status: 'active',
        examsCompleted: 0,
        avgScore: 0,
      };

      const user = await userAPI.create(newUser);
      return { success: true, user };
    } catch (error) {
      return { success: false, error: 'Registration failed' };
    }
  },
};

export default api;
