import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Home, 
  FileText, 
  BookOpen, 
  User, 
  Settings, 
  Users, 
  Bell,
  BarChart3
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

function Sidebar() {
  const { user } = useAuth();

  const studentLinks = [
    { to: '/app', icon: Home, label: 'Dashboard' },
    { to: '/app/exams', icon: FileText, label: 'Exams' },
    { to: '/app/materials', icon: BookOpen, label: 'Study Materials' },
    { to: '/app/profile', icon: User, label: 'Profile' },
  ];

  const adminLinks = [
    { to: '/app/admin', icon: BarChart3, label: 'Admin Dashboard' },
    { to: '/app/admin/users', icon: Users, label: 'Users' },
    { to: '/app/admin/exams', icon: FileText, label: 'Exams' },
    { to: '/app/admin/materials', icon: BookO<PERSON>, label: 'Materials' },
    { to: '/app/admin/notifications', icon: Bell, label: 'Notifications' },
  ];

  const links = user?.role === 'admin' ? adminLinks : studentLinks;

  return (
    <aside className="fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg border-r border-gray-200 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out">
      <div className="flex flex-col h-full pt-16">
        <nav className="flex-1 px-4 py-6 space-y-2">
          {links.map((link) => (
            <NavLink
              key={link.to}
              to={link.to}
              className={({ isActive }) =>
                `flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`
              }
            >
              <link.icon className="h-5 w-5 mr-3" />
              {link.label}
            </NavLink>
          ))}
        </nav>
        
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-500 capitalize">{user?.role}</p>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}

export default Sidebar;