import express from 'express';
import Razorpay from 'razorpay';
import crypto from 'crypto';
import cors from 'cors';
import dotenv from 'dotenv';
import bodyParser from 'body-parser';

dotenv.config();

// Validate environment variables
if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
  console.error('❌ RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET must be set in .env file');
  console.error('📝 Please check your .env file and add your Razorpay API keys');
  process.exit(1);
}

if (process.env.RAZORPAY_KEY_SECRET === 'YOUR_ACTUAL_KEY_SECRET_HERE' ||
  process.env.RAZORPAY_KEY_SECRET === 'your_key_secret') {
  console.error('❌ Please replace YOUR_ACTUAL_KEY_SECRET_HERE with your actual Razorpay key secret');
  console.error('🔗 Get your keys from: https://dashboard.razorpay.com/app/keys');
  process.exit(1);
}

console.log('✅ Razorpay Key ID:', process.env.RAZORPAY_KEY_ID);
console.log('✅ Razorpay Key Secret:', process.env.RAZORPAY_KEY_SECRET.substring(0, 8) + '...');

const app = express();
app.use(cors());
app.use(bodyParser.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Razorpay backend is running',
    razorpay_configured: !!process.env.RAZORPAY_KEY_ID && !!process.env.RAZORPAY_KEY_SECRET
  });
});

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

// Create order for study material purchase
app.post('/create-order', async (req, res) => {
  try {
    const { amount, materialId, materialTitle, userId, userEmail } = req.body;

    // Validate required fields
    if (!amount || !materialId || !userId) {
      return res.status(400).json({
        error: 'Missing required fields: amount, materialId, userId'
      });
    }

    // Convert amount to paise (Razorpay expects amount in smallest currency unit)
    const amountInPaise = Math.round(amount * 100);

    const options = {
      amount: amountInPaise,
      currency: 'INR',
      receipt: `receipt_${materialId}_${Date.now()}`,
      notes: {
        materialId: materialId.toString(),
        materialTitle: materialTitle || 'Study Material',
        userId: userId.toString(),
        userEmail: userEmail || '',
        purchaseType: 'study_material'
      }
    };

    const order = await razorpay.orders.create(options);

    console.log('Order created:', {
      orderId: order.id,
      amount: amount,
      materialId,
      userId
    });

    res.json({
      ...order,
      key_id: process.env.RAZORPAY_KEY_ID // Send key_id for frontend
    });
  } catch (err) {
    console.error('Order creation error:', err);
    res.status(500).json({
      error: 'Unable to create order',
      details: err.message
    });
  }
});

// Verify payment and process purchase
app.post('/verify-payment', async (req, res) => {
  try {
    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      materialId,
      userId
    } = req.body;

    // Verify signature
    const generated_signature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(`${razorpay_order_id}|${razorpay_payment_id}`)
      .digest('hex');

    if (generated_signature !== razorpay_signature) {
      return res.status(400).json({
        status: 'failure',
        message: 'Payment verification failed'
      });
    }

    // Fetch payment details from Razorpay
    const payment = await razorpay.payments.fetch(razorpay_payment_id);
    const order = await razorpay.orders.fetch(razorpay_order_id);

    console.log('Payment verified successfully:', {
      paymentId: razorpay_payment_id,
      orderId: razorpay_order_id,
      amount: payment.amount / 100,
      status: payment.status
    });

    // Here you would typically:
    // 1. Save purchase record to database
    // 2. Grant access to the material
    // 3. Send confirmation email
    // 4. Update user's purchased materials list

    res.json({
      status: 'success',
      message: 'Payment verified successfully',
      paymentDetails: {
        paymentId: razorpay_payment_id,
        orderId: razorpay_order_id,
        amount: payment.amount / 100,
        currency: payment.currency,
        status: payment.status,
        method: payment.method,
        materialId: order.notes?.materialId,
        userId: order.notes?.userId
      }
    });
  } catch (err) {
    console.error('Payment verification error:', err);
    res.status(500).json({
      status: 'error',
      message: 'Payment verification failed',
      details: err.message
    });
  }
});

// Get payment status
app.get('/payment-status/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;
    const payment = await razorpay.payments.fetch(paymentId);

    res.json({
      status: 'success',
      payment: {
        id: payment.id,
        amount: payment.amount / 100,
        currency: payment.currency,
        status: payment.status,
        method: payment.method,
        created_at: payment.created_at
      }
    });
  } catch (err) {
    console.error('Error fetching payment status:', err);
    res.status(500).json({
      status: 'error',
      message: 'Unable to fetch payment status'
    });
  }
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 Razorpay backend running at http://localhost:${PORT}`);
});
