import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import {
  Brain,
  Clock,
  Users,
  Trophy,
  Target,
  BookOpen,
  Play,
  CheckCircle,
  Star,
  TrendingUp,
  Award,
  Calendar,
  ArrowRight,
  Filter,
  Search,
  Timer,
  BarChart3,
  Zap,
  Globe,
  Lightbulb,
  PenTool,
  ChevronRight
} from 'lucide-react';

interface QuizCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  quizCount: number;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
}

interface FeaturedQuiz {
  id: string;
  title: string;
  description: string;
  category: string;
  questions: number;
  duration: string;
  difficulty: string;
  participants: number;
  rating: number;
  image: string;
  tags: string[];
}

interface QuizItem {
  id: string;
  title: string;
  category: string;
  questions: number;
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  participants: number;
  rating: number;
  isNew?: boolean;
  isPopular?: boolean;
}

function Quiz() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [featuredQuizzes, setFeaturedQuizzes] = useState<FeaturedQuiz[]>([]);
  const [allQuizzes, setAllQuizzes] = useState<QuizItem[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // SEO-optimized quiz categories
  const quizCategories: QuizCategory[] = [
    {
      id: 'current-affairs',
      name: 'Current Affairs',
      description: 'Stay updated with latest national and international events',
      icon: Globe,
      color: 'bg-blue-500',
      quizCount: 150,
      difficulty: 'Intermediate',
      estimatedTime: '15-30 min'
    },
    {
      id: 'polity',
      name: 'Indian Polity',
      description: 'Constitution, governance, and political system',
      icon: BookOpen,
      color: 'bg-green-500',
      quizCount: 120,
      difficulty: 'Advanced',
      estimatedTime: '20-45 min'
    },
    {
      id: 'history',
      name: 'Indian History',
      description: 'Ancient, medieval, and modern Indian history',
      icon: Award,
      color: 'bg-purple-500',
      quizCount: 100,
      difficulty: 'Intermediate',
      estimatedTime: '15-30 min'
    },
    {
      id: 'geography',
      name: 'Geography',
      description: 'Physical and human geography of India and world',
      icon: Target,
      color: 'bg-orange-500',
      quizCount: 90,
      difficulty: 'Beginner',
      estimatedTime: '10-25 min'
    },
    {
      id: 'economics',
      name: 'Economics',
      description: 'Indian economy, budget, and economic policies',
      icon: TrendingUp,
      color: 'bg-red-500',
      quizCount: 80,
      difficulty: 'Advanced',
      estimatedTime: '20-40 min'
    },
    {
      id: 'science-tech',
      name: 'Science & Technology',
      description: 'Latest developments in science and technology',
      icon: Zap,
      color: 'bg-indigo-500',
      quizCount: 70,
      difficulty: 'Intermediate',
      estimatedTime: '15-35 min'
    }
  ];

  // Sample data
  useEffect(() => {
    // Featured quizzes
    setFeaturedQuizzes([
      {
        id: '1',
        title: 'UPSC Prelims Mock Test 2024',
        description: 'Comprehensive mock test covering all subjects with detailed explanations',
        category: 'Mock Test',
        questions: 100,
        duration: '2 hours',
        difficulty: 'Advanced',
        participants: 15420,
        rating: 4.8,
        image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
        tags: ['Prelims', 'Full Length', 'All Subjects']
      },
      {
        id: '2',
        title: 'Current Affairs Weekly Quiz',
        description: 'Latest current affairs from national and international events',
        category: 'Current Affairs',
        questions: 50,
        duration: '45 min',
        difficulty: 'Intermediate',
        participants: 8750,
        rating: 4.6,
        image: 'https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg',
        tags: ['Weekly', 'Current Affairs', 'Updated']
      },
      {
        id: '3',
        title: 'Indian Constitution Quiz',
        description: 'Deep dive into constitutional provisions and amendments',
        category: 'Polity',
        questions: 75,
        duration: '90 min',
        difficulty: 'Advanced',
        participants: 6200,
        rating: 4.9,
        image: 'https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg',
        tags: ['Constitution', 'Polity', 'Detailed']
      }
    ]);

    // All quizzes for sidebar
    setAllQuizzes([
      { id: '1', title: 'UPSC Prelims Mock Test 2024', category: 'Mock Test', questions: 100, duration: '2 hours', difficulty: 'Advanced', participants: 15420, rating: 4.8, isPopular: true },
      { id: '2', title: 'Current Affairs Weekly Quiz', category: 'Current Affairs', questions: 50, duration: '45 min', difficulty: 'Intermediate', participants: 8750, rating: 4.6, isNew: true },
      { id: '3', title: 'Indian Constitution Quiz', category: 'Polity', questions: 75, duration: '90 min', difficulty: 'Advanced', participants: 6200, rating: 4.9 },
      { id: '4', title: 'Ancient Indian History', category: 'History', questions: 60, duration: '60 min', difficulty: 'Intermediate', participants: 5400, rating: 4.5 },
      { id: '5', title: 'Indian Geography Basics', category: 'Geography', questions: 40, duration: '30 min', difficulty: 'Beginner', participants: 7200, rating: 4.3 },
      { id: '6', title: 'Economic Survey 2024', category: 'Economics', questions: 55, duration: '50 min', difficulty: 'Advanced', participants: 4800, rating: 4.7, isNew: true },
      { id: '7', title: 'Science & Technology Updates', category: 'Science & Technology', questions: 45, duration: '40 min', difficulty: 'Intermediate', participants: 6100, rating: 4.4 },
      { id: '8', title: 'Fundamental Rights Quiz', category: 'Polity', questions: 35, duration: '25 min', difficulty: 'Beginner', participants: 8900, rating: 4.6 },
      { id: '9', title: 'Medieval India Quiz', category: 'History', questions: 50, duration: '45 min', difficulty: 'Intermediate', participants: 4200, rating: 4.2 },
      { id: '10', title: 'World Geography', category: 'Geography', questions: 65, duration: '55 min', difficulty: 'Advanced', participants: 3800, rating: 4.8, isPopular: true },
      { id: '11', title: 'Budget 2024 Analysis', category: 'Economics', questions: 30, duration: '25 min', difficulty: 'Intermediate', participants: 5600, rating: 4.5, isNew: true },
      { id: '12', title: 'Space Technology Quiz', category: 'Science & Technology', questions: 40, duration: '35 min', difficulty: 'Advanced', participants: 3200, rating: 4.7 },
      { id: '13', title: 'March 2024 Current Affairs', category: 'Current Affairs', questions: 80, duration: '70 min', difficulty: 'Intermediate', participants: 9200, rating: 4.4, isNew: true },
      { id: '14', title: 'Directive Principles Quiz', category: 'Polity', questions: 25, duration: '20 min', difficulty: 'Beginner', participants: 6800, rating: 4.3 },
      { id: '15', title: 'Freedom Struggle Quiz', category: 'History', questions: 70, duration: '60 min', difficulty: 'Advanced', participants: 4600, rating: 4.6, isPopular: true }
    ]);
  }, []);

  const filteredCategories = quizCategories.filter(category =>
    selectedCategory === 'all' || category.id === selectedCategory
  );

  const filteredQuizzes = allQuizzes.filter(quiz => {
    const matchesCategory = selectedCategory === 'all' ||
      quiz.category.toLowerCase().replace(/\s+/g, '-').replace('&', '') === selectedCategory;
    const matchesSearch = quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quiz.category.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <>
      {/* SEO Meta Tags */}
      <Helmet>
        <title>UPSC Quiz - Free Online Mock Tests & Practice Questions | Brainstorm UPSC</title>
        <meta name="description" content="Practice UPSC exam with 500+ free online quizzes. Mock tests for Prelims & Mains, Current Affairs, Polity, History, Geography. Start your UPSC preparation today!" />
        <meta name="keywords" content="UPSC quiz, UPSC mock test, UPSC practice questions, IAS quiz, civil services quiz, UPSC prelims quiz, current affairs quiz, free UPSC test" />
        <meta property="og:title" content="UPSC Quiz - Free Online Mock Tests & Practice Questions" />
        <meta property="og:description" content="Practice UPSC exam with 500+ free online quizzes. Mock tests for all subjects with detailed explanations." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://brainstormupsc.com/quiz" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="UPSC Quiz - Free Online Mock Tests" />
        <meta name="twitter:description" content="Practice UPSC exam with 500+ free online quizzes and mock tests." />
        <link rel="canonical" href="https://brainstormupsc.com/quiz" />

        {/* Structured Data for SEO */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "EducationalOrganization",
            "name": "Brainstorm UPSC Quiz",
            "description": "Free UPSC quiz platform with mock tests and practice questions",
            "url": "https://brainstormupsc.com/quiz",
            "sameAs": [
              "https://facebook.com/brainstormupsc",
              "https://twitter.com/brainstormupsc"
            ],
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "INR",
              "description": "Free UPSC quiz and mock tests"
            }
          })}
        </script>
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <Link to="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Brainstorm UPSC</h1>
                  <p className="text-sm text-gray-600">Quiz Platform</p>
                </div>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link to="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</Link>
                <Link to="/blog" className="text-gray-600 hover:text-blue-600 transition-colors">Blog</Link>
                <Link to="/login" className="text-blue-600 hover:text-blue-700 font-medium">Login</Link>
                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Join Now
                </Link>
              </nav>
            </div>
          </div>
        </header>

        {/* Hero Section */}


        {/* Search and Filter */}
        <section className="py-8 bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search quizzes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-center space-x-4">
                <Filter className="h-5 w-5 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {quizCategories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                <button
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="lg:hidden bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <span>All Quizzes</span>
                  <ChevronRight className={`h-4 w-4 transform transition-transform ${sidebarOpen ? 'rotate-90' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Mobile Sidebar Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content with Sidebar */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex gap-8 relative">
            {/* Sidebar */}
            <aside className={`
              w-80 flex-shrink-0
              ${sidebarOpen ? 'block' : 'hidden lg:block'}
              ${sidebarOpen ? 'fixed lg:relative top-0 left-0 z-50 h-full bg-white lg:bg-transparent p-4 lg:p-0' : ''}
            `}>
              <div className="sticky top-4 lg:top-4">
                <div className="bg-white rounded-xl shadow-lg lg:shadow-sm border border-gray-200 overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4 flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                      <BookOpen className="h-5 w-5" />
                      <span>All Quizzes ({filteredQuizzes.length})</span>
                    </h3>
                    {sidebarOpen && (
                      <button
                        onClick={() => setSidebarOpen(false)}
                        className="lg:hidden text-white hover:text-gray-200 transition-colors"
                      >
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    )}
                  </div>

                  <div className="h-[calc(100vh-16rem)] lg:h-[calc(100vh-12rem)] max-h-[800px] min-h-[400px] overflow-y-auto">
                    {filteredQuizzes.length > 0 ? (
                      <div className="divide-y divide-gray-100">
                        {filteredQuizzes.map((quiz) => (
                          <div key={quiz.id} className="p-4 hover:bg-gray-50 transition-colors cursor-pointer group">
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                                    {quiz.title}
                                  </h4>
                                  {quiz.isNew && (
                                    <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium">
                                      New
                                    </span>
                                  )}
                                  {quiz.isPopular && (
                                    <span className="bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full text-xs font-medium">
                                      Popular
                                    </span>
                                  )}
                                </div>
                                <p className="text-xs text-gray-500 mb-2">{quiz.category}</p>
                                <div className="flex items-center space-x-3 text-xs text-gray-500">
                                  <span className="flex items-center space-x-1">
                                    <PenTool className="h-3 w-3" />
                                    <span>{quiz.questions}Q</span>
                                  </span>
                                  <span className="flex items-center space-x-1">
                                    <Timer className="h-3 w-3" />
                                    <span>{quiz.duration}</span>
                                  </span>
                                  <span className="flex items-center space-x-1">
                                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                                    <span>{quiz.rating}</span>
                                  </span>
                                </div>
                              </div>
                              <div className="ml-2 flex-shrink-0">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${quiz.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                                  quiz.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                  {quiz.difficulty}
                                </span>
                              </div>
                            </div>
                            <div className="mt-2 flex items-center justify-between">
                              <span className="text-xs text-gray-500 flex items-center space-x-1">
                                <Users className="h-3 w-3" />
                                <span>{quiz.participants.toLocaleString()}</span>
                              </span>
                              <button className="text-blue-600 hover:text-blue-700 text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                                Start Quiz →
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-6 text-center text-gray-500">
                        <BookOpen className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p>No quizzes found</p>
                        <p className="text-sm">Try adjusting your search or filter</p>
                      </div>
                    )}
                  </div>

                  {filteredQuizzes.length > 0 && (
                    <div className="bg-gray-50 px-6 py-3 border-t">
                      <button className="w-full text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View All Quizzes →
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </aside>

            {/* Main Content */}
            <main className="flex-1 min-w-0">

              {/* Featured Quizzes */}
              <section className="py-16">
                <div>
                  <div className="text-center mb-12">
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                      Featured Quizzes
                    </h2>
                    <p className="text-xl text-gray-600">
                      Popular quizzes chosen by our expert educators
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {featuredQuizzes.map((quiz) => (
                      <div key={quiz.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
                        <div className="relative">
                          <img
                            src={quiz.image}
                            alt={quiz.title}
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                            {quiz.category}
                          </div>
                          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg flex items-center space-x-1">
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            <span className="text-sm font-medium">{quiz.rating}</span>
                          </div>
                        </div>

                        <div className="p-6">
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">{quiz.title}</h3>
                          <p className="text-gray-600 mb-4">{quiz.description}</p>

                          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                            <div className="flex items-center space-x-4">
                              <span className="flex items-center space-x-1">
                                <PenTool className="h-4 w-4" />
                                <span>{quiz.questions} Questions</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <Timer className="h-4 w-4" />
                                <span>{quiz.duration}</span>
                              </span>
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${quiz.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                              quiz.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                              {quiz.difficulty}
                            </span>
                          </div>

                          <div className="flex items-center justify-between mb-4">
                            <span className="text-sm text-gray-500 flex items-center space-x-1">
                              <Users className="h-4 w-4" />
                              <span>{quiz.participants.toLocaleString()} participants</span>
                            </span>
                          </div>

                          <div className="flex flex-wrap gap-2 mb-4">
                            {quiz.tags.map((tag, index) => (
                              <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                                {tag}
                              </span>
                            ))}
                          </div>

                          <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2">
                            <Play className="h-4 w-4" />
                            <span>Start Quiz</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Quiz Categories */}
              <section className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="text-center mb-12">
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                      Quiz Categories
                    </h2>
                    <p className="text-xl text-gray-600">
                      Choose from comprehensive subject-wise quizzes
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {filteredCategories.map((category) => (
                      <div key={category.id} className="group bg-white rounded-xl border border-gray-200 p-8 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
                        <div className={`w-16 h-16 ${category.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                          <category.icon className="h-8 w-8 text-white" />
                        </div>

                        <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.name}</h3>
                        <p className="text-gray-600 mb-4">{category.description}</p>

                        <div className="space-y-2 mb-6">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Available Quizzes</span>
                            <span className="font-medium text-gray-900">{category.quizCount}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Difficulty</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${category.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                              category.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                              {category.difficulty}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Duration</span>
                            <span className="font-medium text-gray-900">{category.estimatedTime}</span>
                          </div>
                        </div>

                        <button className="w-full bg-gray-900 text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors flex items-center justify-center space-x-2">
                          <span>Explore Quizzes</span>
                          <ArrowRight className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </section>

              {/* Benefits Section */}
              <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="text-center mb-12">
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                      Why Choose Our Quizzes?
                    </h2>
                    <p className="text-xl text-gray-600">
                      Scientifically designed to maximize your UPSC preparation
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div className="bg-white rounded-xl p-8 shadow-sm">
                      <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                        <Lightbulb className="h-6 w-6 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">Instant Feedback</h3>
                      <p className="text-gray-600">Get immediate explanations for every answer with detailed reasoning and references.</p>
                    </div>

                    <div className="bg-white rounded-xl p-8 shadow-sm">
                      <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-6">
                        <BarChart3 className="h-6 w-6 text-green-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">Progress Tracking</h3>
                      <p className="text-gray-600">Monitor your performance with detailed analytics and identify areas for improvement.</p>
                    </div>

                    <div className="bg-white rounded-xl p-8 shadow-sm">
                      <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-6">
                        <Trophy className="h-6 w-6 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">Expert Curated</h3>
                      <p className="text-gray-600">Questions crafted by UPSC toppers and subject matter experts for maximum relevance.</p>
                    </div>
                  </div>
                </div>
              </section>
            </main>
          </div>
        </div>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Ready to Test Your Knowledge?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of UPSC aspirants who are already practicing with our comprehensive quiz platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <span>Start Free Quiz</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
              <Link
                to="/login"
                className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                Login to Continue
              </Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">B</span>
                  </div>
                  <span className="text-xl font-bold">Brainstorm UPSC Quiz</span>
                </div>
                <p className="text-gray-400 mb-4">
                  India's most comprehensive UPSC quiz platform with 500+ practice tests and detailed analytics.
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">Facebook</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">Twitter</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">YouTube</a>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><Link to="/" className="hover:text-white transition-colors">Home</Link></li>
                  <li><Link to="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                  <li><Link to="/login" className="hover:text-white transition-colors">Login</Link></li>
                  <li><Link to="/register" className="hover:text-white transition-colors">Register</Link></li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Categories</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#" className="hover:text-white transition-colors">Current Affairs</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Indian Polity</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">History</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Geography</a></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; 2024 Brainstorm UPSC. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

export default Quiz;
