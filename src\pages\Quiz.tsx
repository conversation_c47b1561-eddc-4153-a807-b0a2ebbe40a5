import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import {
  Brain,
  Clock,
  Users,
  Trophy,
  Target,
  BookOpen,
  Play,
  CheckCircle,
  Star,
  TrendingUp,
  Award,
  Calendar,
  ArrowRight,
  Filter,
  Search,
  Timer,
  BarChart3,
  Zap,
  Globe,
  Lightbulb,
  PenTool
} from 'lucide-react';

interface QuizCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  quizCount: number;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
}

interface FeaturedQuiz {
  id: string;
  title: string;
  description: string;
  category: string;
  questions: number;
  duration: string;
  difficulty: string;
  participants: number;
  rating: number;
  image: string;
  tags: string[];
}

function Quiz() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [featuredQuizzes, setFeaturedQuizzes] = useState<FeaturedQuiz[]>([]);

  // SEO-optimized quiz categories
  const quizCategories: QuizCategory[] = [
    {
      id: 'current-affairs',
      name: 'Current Affairs',
      description: 'Stay updated with latest national and international events',
      icon: Globe,
      color: 'bg-blue-500',
      quizCount: 150,
      difficulty: 'Intermediate',
      estimatedTime: '15-30 min'
    },
    {
      id: 'polity',
      name: 'Indian Polity',
      description: 'Constitution, governance, and political system',
      icon: BookOpen,
      color: 'bg-green-500',
      quizCount: 120,
      difficulty: 'Advanced',
      estimatedTime: '20-45 min'
    },
    {
      id: 'history',
      name: 'Indian History',
      description: 'Ancient, medieval, and modern Indian history',
      icon: Award,
      color: 'bg-purple-500',
      quizCount: 100,
      difficulty: 'Intermediate',
      estimatedTime: '15-30 min'
    },
    {
      id: 'geography',
      name: 'Geography',
      description: 'Physical and human geography of India and world',
      icon: Target,
      color: 'bg-orange-500',
      quizCount: 90,
      difficulty: 'Beginner',
      estimatedTime: '10-25 min'
    },
    {
      id: 'economics',
      name: 'Economics',
      description: 'Indian economy, budget, and economic policies',
      icon: TrendingUp,
      color: 'bg-red-500',
      quizCount: 80,
      difficulty: 'Advanced',
      estimatedTime: '20-40 min'
    },
    {
      id: 'science-tech',
      name: 'Science & Technology',
      description: 'Latest developments in science and technology',
      icon: Zap,
      color: 'bg-indigo-500',
      quizCount: 70,
      difficulty: 'Intermediate',
      estimatedTime: '15-35 min'
    }
  ];

  // Sample featured quizzes
  useEffect(() => {
    setFeaturedQuizzes([
      {
        id: '1',
        title: 'UPSC Prelims Mock Test 2024',
        description: 'Comprehensive mock test covering all subjects with detailed explanations',
        category: 'Mock Test',
        questions: 100,
        duration: '2 hours',
        difficulty: 'Advanced',
        participants: 15420,
        rating: 4.8,
        image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
        tags: ['Prelims', 'Full Length', 'All Subjects']
      },
      {
        id: '2',
        title: 'Current Affairs Weekly Quiz',
        description: 'Latest current affairs from national and international events',
        category: 'Current Affairs',
        questions: 50,
        duration: '45 min',
        difficulty: 'Intermediate',
        participants: 8750,
        rating: 4.6,
        image: 'https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg',
        tags: ['Weekly', 'Current Affairs', 'Updated']
      },
      {
        id: '3',
        title: 'Indian Constitution Quiz',
        description: 'Deep dive into constitutional provisions and amendments',
        category: 'Polity',
        questions: 75,
        duration: '90 min',
        difficulty: 'Advanced',
        participants: 6200,
        rating: 4.9,
        image: 'https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg',
        tags: ['Constitution', 'Polity', 'Detailed']
      }
    ]);
  }, []);

  const filteredCategories = quizCategories.filter(category =>
    selectedCategory === 'all' || category.id === selectedCategory
  );

  return (
    <>
      {/* SEO Meta Tags */}
      <Helmet>
        <title>UPSC Quiz - Free Online Mock Tests & Practice Questions | Brainstorm UPSC</title>
        <meta name="description" content="Practice UPSC exam with 500+ free online quizzes. Mock tests for Prelims & Mains, Current Affairs, Polity, History, Geography. Start your UPSC preparation today!" />
        <meta name="keywords" content="UPSC quiz, UPSC mock test, UPSC practice questions, IAS quiz, civil services quiz, UPSC prelims quiz, current affairs quiz, free UPSC test" />
        <meta property="og:title" content="UPSC Quiz - Free Online Mock Tests & Practice Questions" />
        <meta property="og:description" content="Practice UPSC exam with 500+ free online quizzes. Mock tests for all subjects with detailed explanations." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://brainstormupsc.com/quiz" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="UPSC Quiz - Free Online Mock Tests" />
        <meta name="twitter:description" content="Practice UPSC exam with 500+ free online quizzes and mock tests." />
        <link rel="canonical" href="https://brainstormupsc.com/quiz" />

        {/* Structured Data for SEO */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "EducationalOrganization",
            "name": "Brainstorm UPSC Quiz",
            "description": "Free UPSC quiz platform with mock tests and practice questions",
            "url": "https://brainstormupsc.com/quiz",
            "sameAs": [
              "https://facebook.com/brainstormupsc",
              "https://twitter.com/brainstormupsc"
            ],
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "INR",
              "description": "Free UPSC quiz and mock tests"
            }
          })}
        </script>
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <Link to="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Brainstorm UPSC</h1>
                  <p className="text-sm text-gray-600">Quiz Platform</p>
                </div>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link to="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</Link>
                <Link to="/blog" className="text-gray-600 hover:text-blue-600 transition-colors">Blog</Link>
                <Link to="/login" className="text-blue-600 hover:text-blue-700 font-medium">Login</Link>
                <Link to="/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Join Now
                </Link>
              </nav>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <Brain className="h-4 w-4" />
                <span>500+ Free UPSC Quizzes</span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                Master UPSC with
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"> Smart Quizzes</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Practice with expertly crafted quizzes covering all UPSC subjects. Get instant feedback, detailed explanations, and track your progress towards IAS success.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2 shadow-lg">
                  <Play className="h-5 w-5" />
                  <span>Start Quiz Now</span>
                </button>
                <button className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:border-blue-600 hover:text-blue-600 transition-all flex items-center justify-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>View Analytics</span>
                </button>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
                <div className="text-gray-600">Practice Quizzes</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-green-600 mb-2">50K+</div>
                <div className="text-gray-600">Questions Solved</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-purple-600 mb-2">15K+</div>
                <div className="text-gray-600">Active Users</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-3xl font-bold text-orange-600 mb-2">4.8★</div>
                <div className="text-gray-600">User Rating</div>
              </div>
            </div>
          </div>
        </section>

        {/* Search and Filter */}
        <section className="py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search quizzes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-center space-x-4">
                <Filter className="h-5 w-5 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {quizCategories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Quizzes */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Featured Quizzes
              </h2>
              <p className="text-xl text-gray-600">
                Popular quizzes chosen by our expert educators
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredQuizzes.map((quiz) => (
                <div key={quiz.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img
                      src={quiz.image}
                      alt={quiz.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {quiz.category}
                    </div>
                    <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{quiz.rating}</span>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{quiz.title}</h3>
                    <p className="text-gray-600 mb-4">{quiz.description}</p>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center space-x-1">
                          <PenTool className="h-4 w-4" />
                          <span>{quiz.questions} Questions</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Timer className="h-4 w-4" />
                          <span>{quiz.duration}</span>
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${quiz.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                          quiz.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                        }`}>
                        {quiz.difficulty}
                      </span>
                    </div>

                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-500 flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{quiz.participants.toLocaleString()} participants</span>
                      </span>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {quiz.tags.map((tag, index) => (
                        <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>

                    <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2">
                      <Play className="h-4 w-4" />
                      <span>Start Quiz</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Quiz Categories */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Quiz Categories
              </h2>
              <p className="text-xl text-gray-600">
                Choose from comprehensive subject-wise quizzes
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredCategories.map((category) => (
                <div key={category.id} className="group bg-white rounded-xl border border-gray-200 p-8 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
                  <div className={`w-16 h-16 ${category.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                    <category.icon className="h-8 w-8 text-white" />
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.name}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>

                  <div className="space-y-2 mb-6">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Available Quizzes</span>
                      <span className="font-medium text-gray-900">{category.quizCount}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Difficulty</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${category.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                          category.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                        }`}>
                        {category.difficulty}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Duration</span>
                      <span className="font-medium text-gray-900">{category.estimatedTime}</span>
                    </div>
                  </div>

                  <button className="w-full bg-gray-900 text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors flex items-center justify-center space-x-2">
                    <span>Explore Quizzes</span>
                    <ArrowRight className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Why Choose Our Quizzes?
              </h2>
              <p className="text-xl text-gray-600">
                Scientifically designed to maximize your UPSC preparation
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-white rounded-xl p-8 shadow-sm">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                  <Lightbulb className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Instant Feedback</h3>
                <p className="text-gray-600">Get immediate explanations for every answer with detailed reasoning and references.</p>
              </div>

              <div className="bg-white rounded-xl p-8 shadow-sm">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-6">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Progress Tracking</h3>
                <p className="text-gray-600">Monitor your performance with detailed analytics and identify areas for improvement.</p>
              </div>

              <div className="bg-white rounded-xl p-8 shadow-sm">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-6">
                  <Trophy className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Expert Curated</h3>
                <p className="text-gray-600">Questions crafted by UPSC toppers and subject matter experts for maximum relevance.</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Ready to Test Your Knowledge?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of UPSC aspirants who are already practicing with our comprehensive quiz platform
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <span>Start Free Quiz</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
              <Link
                to="/login"
                className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                Login to Continue
              </Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">B</span>
                  </div>
                  <span className="text-xl font-bold">Brainstorm UPSC Quiz</span>
                </div>
                <p className="text-gray-400 mb-4">
                  India's most comprehensive UPSC quiz platform with 500+ practice tests and detailed analytics.
                </p>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">Facebook</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">Twitter</a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">YouTube</a>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><Link to="/" className="hover:text-white transition-colors">Home</Link></li>
                  <li><Link to="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                  <li><Link to="/login" className="hover:text-white transition-colors">Login</Link></li>
                  <li><Link to="/register" className="hover:text-white transition-colors">Register</Link></li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-4">Categories</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#" className="hover:text-white transition-colors">Current Affairs</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Indian Polity</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">History</a></li>
                  <li><a href="#" className="hover:text-white transition-colors">Geography</a></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; 2024 Brainstorm UPSC. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}

export default Quiz;
